# Windows Mobile Hotspot Manager

A comprehensive Windows application for managing Mobile Hotspot functionality with advanced features including device management, bandwidth control, security settings, and automation capabilities.

## Features

### Core Functionality
- **Complete Hotspot Control**: Start, stop, and restart mobile hotspot with full configuration options
- **Windows API Integration**: Direct integration with Windows WLAN Hosted Network API for low-level control
- **PowerShell Automation**: Comprehensive PowerShell module for scripting and automation
- **Modern UI**: Material Design-based WPF interface with real-time monitoring

### Advanced Features
- **Device Management**: Monitor, disconnect, block/unblock connected devices
- **Bandwidth Control**: Set overall and per-device bandwidth limits
- **Security Configuration**: Support for WPA2/WPA3, custom passwords, and hidden networks
- **Network Analytics**: Real-time traffic monitoring and usage statistics
- **Event Logging**: Comprehensive audit trail of all hotspot activities
- **Profile Management**: Save and load multiple hotspot configurations
- **Scheduling**: Auto-start/stop hotspot at specified times
- **Guest Network Isolation**: Isolate guest devices from local network

### Enterprise Features
- **DHCP Configuration**: Custom IP ranges, reservations, and lease times
- **DNS Settings**: Custom DNS servers and domain filtering
- **Access Control**: Device whitelisting/blacklisting with MAC address management
- **Rate Limiting**: Advanced QoS and traffic shaping capabilities
- **Monitoring Dashboard**: Real-time connection status and performance metrics
- **Export/Import**: Configuration backup and restore functionality

## System Requirements

- **Operating System**: Windows 10/11 (64-bit)
- **Framework**: .NET 8.0 or later
- **Privileges**: Administrator rights required for hotspot operations
- **Hardware**: Wireless adapter with hosted network support

## Installation

### Prerequisites
1. Install .NET 8.0 Runtime or SDK
2. Ensure your wireless adapter supports hosted networks
3. Run PowerShell as Administrator for full functionality

### Build from Source
```bash
# Clone the repository
git clone <repository-url>
cd hotspot

# Build the solution
dotnet build HotspotManager.sln --configuration Release

# Run the application
dotnet run --project src/HotspotManager.UI
```

### PowerShell Module Installation
```powershell
# Import the PowerShell module
Import-Module .\scripts\HotspotManager.psm1

# Verify installation
Get-Command -Module HotspotManager
```

## Usage

### GUI Application
1. Launch `HotspotManager.UI.exe` as Administrator
2. Configure hotspot settings in the Configuration tab
3. Start the hotspot from the Dashboard
4. Monitor connected devices and statistics in real-time

### PowerShell Commands

#### Basic Operations
```powershell
# Start hotspot with basic settings
Start-Hotspot -SSID "MyHotspot" -Password "SecurePass123"

# Stop hotspot
Stop-Hotspot

# Get hotspot status
Get-HotspotStatus

# Get connected devices
Get-HotspotDevices
```

#### Advanced Configuration
```powershell
# Start with advanced settings
Enable-AdvancedHotspot -SSID "GuestNetwork" -Password "GuestPass123" `
    -MaxConnections 5 -Hidden -GuestIsolation -BandwidthLimitMbps 10

# Device management
Block-HotspotDevice -MacAddress "AA:BB:CC:DD:EE:FF" -Reason "Security violation"
Remove-HotspotDevice -MacAddress "AA:BB:CC:DD:EE:FF" -Block

# Profile management
Save-HotspotProfile -ProfileName "HomeNetwork"
Load-HotspotProfile -ProfileName "GuestNetwork" -Apply

# Scheduling
Schedule-HotspotOperation -Action Start -Time "08:00" -SSID "WorkHotspot"
Schedule-HotspotOperation -Action Stop -Time "18:00"
```

### Configuration Examples

#### Basic Home Network
```json
{
  "SSID": "Home-WiFi-Guest",
  "Password": "WelcomeGuest123",
  "MaxConnections": 5,
  "Security": "WPA2_PSK",
  "GuestNetworkIsolation": true
}
```

#### Enterprise Guest Network
```json
{
  "SSID": "Company-Guest",
  "Password": "TempAccess2024",
  "MaxConnections": 20,
  "Security": "WPA3_SAE",
  "BandwidthLimits": {
    "DownloadLimitMbps": 50,
    "UploadLimitMbps": 10,
    "PerDeviceDownloadLimitMbps": 5
  },
  "DhcpConfig": {
    "StartIP": "**************",
    "EndIP": "**************0",
    "LeaseTime": "02:00:00"
  },
  "DnsConfig": {
    "PrimaryDNS": "*******",
    "SecondaryDNS": "*******",
    "EnableDnsFiltering": true,
    "BlockedDomains": ["social-media.com", "streaming.com"]
  }
}
```

## Architecture

### Core Components
- **HotspotManager.Core**: Core business logic and Windows API wrappers
- **HotspotManager.UI**: WPF-based user interface with Material Design
- **HotspotManager.PowerShell**: PowerShell cmdlets and automation scripts
- **HotspotManager.Tests**: Comprehensive unit and integration tests

### Key Classes
- `WindowsHotspotService`: Main service implementing `IHotspotService`
- `WlanApi`: Native Windows API wrapper for hosted network functionality
- `HotspotConfiguration`: Configuration model with validation
- `ConnectedDevice`: Device information and statistics
- `NetworkStatistics`: Real-time network performance metrics

## Security Considerations

### Network Security
- WPA2/WPA3 encryption support
- Custom password requirements (8-63 characters)
- Hidden network option for additional security
- Guest network isolation to protect local resources

### Application Security
- Administrator privileges required for system-level operations
- Secure storage of configuration profiles
- Input validation and sanitization
- Audit logging of all security-related events

### Best Practices
- Use strong passwords (minimum 12 characters recommended)
- Enable guest network isolation for public access
- Regularly monitor connected devices
- Set appropriate bandwidth limits to prevent abuse
- Review event logs for suspicious activity

## Troubleshooting

### Common Issues

#### Hotspot Not Available
- Verify wireless adapter supports hosted networks
- Check if Mobile Hotspot is enabled in Windows Settings
- Ensure no other applications are using the wireless adapter
- Run `netsh wlan show drivers` to check hosted network support

#### Connection Issues
- Verify firewall settings allow hotspot traffic
- Check if Internet Connection Sharing is enabled
- Ensure DHCP service is running
- Verify network adapter configuration

#### Performance Issues
- Monitor CPU and memory usage during operation
- Check for driver updates for wireless adapter
- Adjust bandwidth limits if experiencing congestion
- Review event logs for error messages

### Diagnostic Commands
```powershell
# Check hosted network support
netsh wlan show drivers

# View hosted network settings
netsh wlan show hostednetwork

# Check network adapters
Get-NetAdapter | Where-Object {$_.InterfaceDescription -like "*Hosted*"}

# Test connectivity
Test-NetConnection -ComputerName ******* -Port 53
```

## Development

### Building the Project
```bash
# Restore dependencies
dotnet restore

# Build all projects
dotnet build --configuration Release

# Run tests
dotnet test

# Publish for deployment
dotnet publish src/HotspotManager.UI --configuration Release --self-contained
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes with appropriate tests
4. Submit a pull request with detailed description

### Code Style
- Follow C# coding conventions
- Use meaningful variable and method names
- Add XML documentation for public APIs
- Include unit tests for new functionality
- Follow MVVM pattern for UI components

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues, questions, or feature requests:
1. Check the troubleshooting section above
2. Review existing GitHub issues
3. Create a new issue with detailed information
4. Include system information and error logs

## Acknowledgments

- Microsoft for Windows WLAN API documentation
- Material Design In XAML Toolkit for UI components
- Community contributors and testers
