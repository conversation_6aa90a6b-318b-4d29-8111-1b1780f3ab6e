#Requires -Version 5.1

<#
.SYNOPSIS
    Build script for Windows Mobile Hotspot Manager
.DESCRIPTION
    This script builds the HotspotManager solution, runs tests, and optionally packages the application for distribution.
.PARAMETER Configuration
    Build configuration (Debug or Release)
.PARAMETER RunTests
    Run unit tests after building
.PARAMETER Package
    Create a distribution package
.PARAMETER Clean
    Clean the solution before building
.PARAMETER Verbose
    Enable verbose output
.EXAMPLE
    .\build.ps1 -Configuration Release -RunTests -Package
.EXAMPLE
    .\build.ps1 -Clean -Configuration Debug
#>

param(
    [ValidateSet("Debug", "Release")]
    [string]$Configuration = "Release",
    
    [switch]$RunTests,
    [switch]$Package,
    [switch]$Clean,
    [switch]$Verbose
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Function to execute command with error handling
function Invoke-Command {
    param(
        [string]$Command,
        [string]$Description
    )
    
    Write-ColorOutput "Executing: $Description" "Yellow"
    if ($Verbose) {
        Write-ColorOutput "Command: $Command" "Gray"
    }
    
    try {
        Invoke-Expression $Command
        if ($LASTEXITCODE -ne 0) {
            throw "Command failed with exit code $LASTEXITCODE"
        }
        Write-ColorOutput "✓ $Description completed successfully" "Green"
    }
    catch {
        Write-ColorOutput "✗ $Description failed: $($_.Exception.Message)" "Red"
        throw
    }
}

# Function to check prerequisites
function Test-Prerequisites {
    Write-ColorOutput "Checking prerequisites..." "Yellow"
    
    # Check .NET SDK
    try {
        $dotnetVersion = dotnet --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw ".NET SDK not found"
        }
        Write-ColorOutput "✓ .NET SDK version: $dotnetVersion" "Green"
    }
    catch {
        Write-ColorOutput "✗ .NET SDK 8.0 or later is required. Please install from https://dotnet.microsoft.com/" "Red"
        throw
    }
    
    # Check solution file
    if (-not (Test-Path "HotspotManager.sln")) {
        throw "Solution file 'HotspotManager.sln' not found in current directory"
    }
    Write-ColorOutput "✓ Solution file found" "Green"
    
    Write-ColorOutput "Prerequisites check completed" "Green"
}

# Function to clean solution
function Invoke-Clean {
    Write-ColorOutput "Cleaning solution..." "Yellow"
    
    # Clean using dotnet
    Invoke-Command "dotnet clean HotspotManager.sln --configuration $Configuration" "Clean solution"
    
    # Remove bin and obj directories
    $dirsToRemove = @("bin", "obj")
    Get-ChildItem -Path . -Recurse -Directory | Where-Object { $_.Name -in $dirsToRemove } | ForEach-Object {
        if ($Verbose) {
            Write-ColorOutput "Removing directory: $($_.FullName)" "Gray"
        }
        Remove-Item $_.FullName -Recurse -Force -ErrorAction SilentlyContinue
    }
    
    Write-ColorOutput "✓ Solution cleaned" "Green"
}

# Function to restore packages
function Invoke-Restore {
    Write-ColorOutput "Restoring NuGet packages..." "Yellow"
    Invoke-Command "dotnet restore HotspotManager.sln" "Restore packages"
}

# Function to build solution
function Invoke-Build {
    Write-ColorOutput "Building solution..." "Yellow"
    
    $buildArgs = "build HotspotManager.sln --configuration $Configuration --no-restore"
    if ($Verbose) {
        $buildArgs += " --verbosity detailed"
    }
    
    Invoke-Command "dotnet $buildArgs" "Build solution"
}

# Function to run tests
function Invoke-Tests {
    Write-ColorOutput "Running tests..." "Yellow"
    
    $testArgs = "test tests/HotspotManager.Tests/HotspotManager.Tests.csproj --configuration $Configuration --no-build --logger trx --results-directory TestResults"
    if ($Verbose) {
        $testArgs += " --verbosity detailed"
    }
    
    try {
        Invoke-Command "dotnet $testArgs" "Run unit tests"
        
        # Display test results summary
        $testResults = Get-ChildItem -Path "TestResults" -Filter "*.trx" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 1
        if ($testResults) {
            Write-ColorOutput "Test results saved to: $($testResults.FullName)" "Cyan"
        }
    }
    catch {
        Write-ColorOutput "Some tests failed. Check the test results for details." "Yellow"
        # Don't throw here to allow packaging even if some tests fail
    }
}

# Function to publish application
function Invoke-Publish {
    Write-ColorOutput "Publishing application..." "Yellow"
    
    $publishDir = "publish"
    if (Test-Path $publishDir) {
        Remove-Item $publishDir -Recurse -Force
    }
    
    # Publish UI application
    $publishArgs = "publish src/HotspotManager.UI/HotspotManager.UI.csproj --configuration $Configuration --output $publishDir --self-contained false --runtime win-x64"
    Invoke-Command "dotnet $publishArgs" "Publish UI application"
    
    # Copy PowerShell scripts
    $scriptsSource = "scripts"
    $scriptsDestination = Join-Path $publishDir "scripts"
    if (Test-Path $scriptsSource) {
        Copy-Item -Path $scriptsSource -Destination $scriptsDestination -Recurse -Force
        Write-ColorOutput "✓ PowerShell scripts copied to publish directory" "Green"
    }
    
    # Copy README and documentation
    $docsToInclude = @("README.md", "LICENSE")
    foreach ($doc in $docsToInclude) {
        if (Test-Path $doc) {
            Copy-Item -Path $doc -Destination $publishDir -Force
            Write-ColorOutput "✓ $doc copied to publish directory" "Green"
        }
    }
    
    Write-ColorOutput "✓ Application published to: $publishDir" "Green"
}

# Function to create package
function New-Package {
    Write-ColorOutput "Creating distribution package..." "Yellow"
    
    $publishDir = "publish"
    if (-not (Test-Path $publishDir)) {
        throw "Publish directory not found. Run with -Package to publish first."
    }
    
    # Create package directory
    $packageName = "HotspotManager-v1.0.0-win-x64"
    $packageDir = "packages"
    $fullPackagePath = Join-Path $packageDir $packageName
    
    if (Test-Path $packageDir) {
        Remove-Item $packageDir -Recurse -Force
    }
    New-Item -Path $fullPackagePath -ItemType Directory -Force | Out-Null
    
    # Copy published files
    Copy-Item -Path "$publishDir\*" -Destination $fullPackagePath -Recurse -Force
    
    # Create ZIP package
    $zipPath = "$packageDir\$packageName.zip"
    try {
        Compress-Archive -Path $fullPackagePath -DestinationPath $zipPath -Force
        Write-ColorOutput "✓ ZIP package created: $zipPath" "Green"
    }
    catch {
        Write-ColorOutput "⚠ Failed to create ZIP package: $($_.Exception.Message)" "Yellow"
    }
    
    # Create installer script
    $installerContent = @"
# HotspotManager Quick Installer
# Extract this package and run Install-HotspotManager.ps1 as Administrator

Write-Host "HotspotManager Installation Package" -ForegroundColor Cyan
Write-Host "====================================="
Write-Host ""
Write-Host "To install HotspotManager:"
Write-Host "1. Extract this package to a temporary directory"
Write-Host "2. Open PowerShell as Administrator"
Write-Host "3. Navigate to the extracted directory"
Write-Host "4. Run: .\scripts\Install-HotspotManager.ps1"
Write-Host ""
Write-Host "For manual installation:"
Write-Host "1. Copy the contents to your desired installation directory"
Write-Host "2. Run HotspotManager.UI.exe as Administrator"
Write-Host ""
Write-Host "System Requirements:"
Write-Host "- Windows 10/11 (64-bit)"
Write-Host "- .NET 8.0 Runtime"
Write-Host "- Administrator privileges"
Write-Host "- Wireless adapter with hosted network support"
Write-Host ""
"@
    
    Set-Content -Path (Join-Path $fullPackagePath "INSTALL.txt") -Value $installerContent -Encoding UTF8
    
    Write-ColorOutput "✓ Distribution package created: $fullPackagePath" "Green"
    
    # Display package contents
    Write-ColorOutput "`nPackage contents:" "Cyan"
    Get-ChildItem -Path $fullPackagePath -Recurse | ForEach-Object {
        $relativePath = $_.FullName.Substring($fullPackagePath.Length + 1)
        Write-ColorOutput "  $relativePath" "Gray"
    }
}

# Function to display build summary
function Show-BuildSummary {
    param(
        [DateTime]$StartTime,
        [bool]$Success
    )
    
    $duration = (Get-Date) - $StartTime
    
    Write-ColorOutput "`n=== Build Summary ===" "Cyan"
    Write-ColorOutput "Configuration: $Configuration" "White"
    Write-ColorOutput "Duration: $($duration.ToString('mm\:ss'))" "White"
    Write-ColorOutput "Status: $(if ($Success) { 'SUCCESS' } else { 'FAILED' })" $(if ($Success) { "Green" } else { "Red" })
    
    if ($Success) {
        Write-ColorOutput "`nNext steps:" "Yellow"
        Write-ColorOutput "- Run the application: .\publish\HotspotManager.UI.exe" "White"
        Write-ColorOutput "- Import PowerShell module: Import-Module .\publish\scripts\HotspotManager.psm1" "White"
        Write-ColorOutput "- Install system-wide: .\publish\scripts\Install-HotspotManager.ps1" "White"
    }
    
    Write-ColorOutput ""
}

# Main build process
try {
    $startTime = Get-Date
    
    Write-ColorOutput "=== Windows Mobile Hotspot Manager Build Script ===" "Cyan"
    Write-ColorOutput "Configuration: $Configuration" "White"
    Write-ColorOutput "Start time: $($startTime.ToString('yyyy-MM-dd HH:mm:ss'))" "White"
    Write-ColorOutput ""
    
    # Check prerequisites
    Test-Prerequisites
    Write-ColorOutput ""
    
    # Clean if requested
    if ($Clean) {
        Invoke-Clean
        Write-ColorOutput ""
    }
    
    # Restore packages
    Invoke-Restore
    Write-ColorOutput ""
    
    # Build solution
    Invoke-Build
    Write-ColorOutput ""
    
    # Run tests if requested
    if ($RunTests) {
        Invoke-Tests
        Write-ColorOutput ""
    }
    
    # Publish and package if requested
    if ($Package) {
        Invoke-Publish
        Write-ColorOutput ""
        
        New-Package
        Write-ColorOutput ""
    }
    
    Show-BuildSummary -StartTime $startTime -Success $true
}
catch {
    Write-ColorOutput ""
    Write-ColorOutput "Build failed: $($_.Exception.Message)" "Red"
    Show-BuildSummary -StartTime $startTime -Success $false
    exit 1
}
