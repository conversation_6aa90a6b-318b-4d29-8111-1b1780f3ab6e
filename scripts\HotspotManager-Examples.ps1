# HotspotManager PowerShell Examples
# This script demonstrates various usage scenarios for the HotspotManager PowerShell module

#Requires -Version 5.1
#Requires -RunAsAdministrator

# Import the module
Import-Module HotspotManager -Force

Write-Host "=== HotspotManager PowerShell Examples ===" -ForegroundColor Cyan
Write-Host ""

# Example 1: Basic Hotspot Operations
Write-Host "Example 1: Basic Hotspot Operations" -ForegroundColor Yellow
Write-Host "-----------------------------------"

# Start a basic hotspot
Write-Host "Starting basic hotspot..."
$result = Start-Hotspot -SSID "TestHotspot" -Password "TestPass123" -Verbose
if ($result) {
    Write-Host "✓ Hotspot started successfully" -ForegroundColor Green
    
    # Get status
    Write-Host "Getting hotspot status..."
    $status = Get-HotspotStatus
    Write-Host "Status: $($status.IsActive ? 'Active' : 'Inactive')" -ForegroundColor ($status.IsActive ? 'Green' : 'Red')
    
    # Wait a moment
    Start-Sleep -Seconds 3
    
    # Stop the hotspot
    Write-Host "Stopping hotspot..."
    Stop-Hotspot -Force
    Write-Host "✓ Hotspot stopped" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to start hotspot" -ForegroundColor Red
}

Write-Host ""

# Example 2: Advanced Hotspot Configuration
Write-Host "Example 2: Advanced Hotspot Configuration" -ForegroundColor Yellow
Write-Host "----------------------------------------"

Write-Host "Starting advanced hotspot with custom settings..."
$advancedResult = Enable-AdvancedHotspot -SSID "AdvancedHotspot" -Password "SecurePass123" `
    -MaxConnections 5 -Security "WPA2_PSK" -Hidden -GuestIsolation `
    -BandwidthLimitMbps 20 -ProfileName "AdvancedProfile" -Verbose

if ($advancedResult) {
    Write-Host "✓ Advanced hotspot started" -ForegroundColor Green
    
    # Show detailed info
    Get-HotspotInfo
    
    # Stop after demonstration
    Start-Sleep -Seconds 2
    Disable-AdvancedHotspot -Force
} else {
    Write-Host "✗ Failed to start advanced hotspot" -ForegroundColor Red
}

Write-Host ""

# Example 3: Profile Management
Write-Host "Example 3: Profile Management" -ForegroundColor Yellow
Write-Host "----------------------------"

# Create sample profiles
$profiles = @(
    @{
        Name = "HomeNetwork"
        SSID = "Home-Guest-WiFi"
        Password = "WelcomeHome123"
        MaxConnections = 8
        Security = "WPA2_PSK"
    },
    @{
        Name = "OfficeNetwork"
        SSID = "Office-Temp"
        Password = "TempAccess2024"
        MaxConnections = 3
        Security = "WPA3_SAE"
        Hidden = $true
    },
    @{
        Name = "EventNetwork"
        SSID = "Event-WiFi"
        Password = "EventGuest123"
        MaxConnections = 20
        BandwidthLimitMbps = 10
        GuestIsolation = $true
    }
)

Write-Host "Creating sample profiles..."
foreach ($profile in $profiles) {
    try {
        # Start hotspot with profile settings
        $params = @{
            SSID = $profile.SSID
            Password = $profile.Password
            MaxConnections = $profile.MaxConnections
            Security = $profile.Security
            ProfileName = $profile.Name
        }
        
        if ($profile.Hidden) { $params.Hidden = $true }
        if ($profile.BandwidthLimitMbps) { $params.BandwidthLimitMbps = $profile.BandwidthLimitMbps }
        if ($profile.GuestIsolation) { $params.GuestIsolation = $true }
        
        Enable-AdvancedHotspot @params
        Write-Host "✓ Profile '$($profile.Name)' created and saved" -ForegroundColor Green
        
        # Stop immediately for demo
        Disable-AdvancedHotspot -Force
        Start-Sleep -Seconds 1
    }
    catch {
        Write-Host "✗ Failed to create profile '$($profile.Name)': $($_.Exception.Message)" -ForegroundColor Red
    }
}

# List saved profiles
Write-Host "`nSaved profiles:"
try {
    $savedProfiles = Get-ChildItem "$env:APPDATA\HotspotManager\Profiles" -Filter "*.json" -ErrorAction SilentlyContinue
    foreach ($profileFile in $savedProfiles) {
        $profileName = [System.IO.Path]::GetFileNameWithoutExtension($profileFile.Name)
        Write-Host "  - $profileName" -ForegroundColor Cyan
    }
} catch {
    Write-Host "  No profiles found" -ForegroundColor Gray
}

Write-Host ""

# Example 4: Device Management Simulation
Write-Host "Example 4: Device Management Simulation" -ForegroundColor Yellow
Write-Host "--------------------------------------"

Write-Host "Starting hotspot for device management demo..."
$deviceDemoResult = Enable-AdvancedHotspot -SSID "DeviceDemo" -Password "DeviceTest123" -MaxConnections 5

if ($deviceDemoResult) {
    Write-Host "✓ Hotspot started for device management demo" -ForegroundColor Green
    
    # Simulate device management operations
    $sampleDevices = @(
        "AA:BB:CC:DD:EE:01",
        "AA:BB:CC:DD:EE:02",
        "AA:BB:CC:DD:EE:03"
    )
    
    Write-Host "`nSimulating device management operations..."
    
    foreach ($mac in $sampleDevices) {
        Write-Host "Managing device: $mac"
        
        # Block device
        Manage-HotspotDevice -MacAddress $mac -Action Block -Reason "Demo block"
        Write-Host "  ✓ Device blocked" -ForegroundColor Yellow
        
        Start-Sleep -Seconds 1
        
        # Unblock device
        Manage-HotspotDevice -MacAddress $mac -Action Unblock
        Write-Host "  ✓ Device unblocked" -ForegroundColor Green
    }
    
    # Get connected devices (will be empty in demo)
    Write-Host "`nChecking connected devices..."
    $devices = Get-HotspotDevices
    if ($devices.Count -gt 0) {
        Write-Host "Connected devices:" -ForegroundColor Cyan
        $devices | Format-Table -Property MacAddress, IPAddress, DeviceName, ConnectedAt -AutoSize
    } else {
        Write-Host "No devices currently connected" -ForegroundColor Gray
    }
    
    # Stop demo hotspot
    Disable-AdvancedHotspot -Force
    Write-Host "✓ Device management demo completed" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to start device management demo" -ForegroundColor Red
}

Write-Host ""

# Example 5: Scheduling Operations
Write-Host "Example 5: Scheduling Operations" -ForegroundColor Yellow
Write-Host "-------------------------------"

Write-Host "Demonstrating scheduling operations..."

# Schedule start for 1 minute from now
$startTime = (Get-Date).AddMinutes(1).ToString("HH:mm")
Write-Host "Scheduling hotspot to start at $startTime..."

$scheduleResult = Schedule-HotspotOperation -Action Start -Time $startTime -SSID "ScheduledHotspot" -Password "ScheduledPass123"
if ($scheduleResult) {
    Write-Host "✓ Start operation scheduled for $startTime" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to schedule start operation" -ForegroundColor Red
}

# Schedule stop for 2 minutes from now
$stopTime = (Get-Date).AddMinutes(2).ToString("HH:mm")
Write-Host "Scheduling hotspot to stop at $stopTime..."

$scheduleStopResult = Schedule-HotspotOperation -Action Stop -Time $stopTime
if ($scheduleStopResult) {
    Write-Host "✓ Stop operation scheduled for $stopTime" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to schedule stop operation" -ForegroundColor Red
}

Write-Host ""

# Example 6: Profile Loading and Application
Write-Host "Example 6: Profile Loading and Application" -ForegroundColor Yellow
Write-Host "----------------------------------------"

Write-Host "Loading and applying a saved profile..."
try {
    # Try to load the first profile we created
    $profileToLoad = "HomeNetwork"
    Write-Host "Loading profile: $profileToLoad"
    
    $loadResult = Load-HotspotProfile -ProfileName $profileToLoad -Apply
    if ($loadResult) {
        Write-Host "✓ Profile '$profileToLoad' loaded and applied" -ForegroundColor Green
        
        # Show current status
        Get-HotspotInfo
        
        # Stop after demo
        Start-Sleep -Seconds 3
        Disable-AdvancedHotspot -Force
        Write-Host "✓ Profile demo completed" -ForegroundColor Green
    } else {
        Write-Host "✗ Failed to load profile '$profileToLoad'" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Profile loading error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Example 7: Comprehensive Status Monitoring
Write-Host "Example 7: Comprehensive Status Monitoring" -ForegroundColor Yellow
Write-Host "-----------------------------------------"

Write-Host "Starting hotspot for status monitoring demo..."
$monitorResult = Enable-AdvancedHotspot -SSID "MonitorDemo" -Password "MonitorTest123" -MaxConnections 3

if ($monitorResult) {
    Write-Host "✓ Hotspot started for monitoring demo" -ForegroundColor Green
    
    Write-Host "`nMonitoring hotspot status for 10 seconds..."
    for ($i = 1; $i -le 5; $i++) {
        Write-Host "Status check $i/5:" -ForegroundColor Cyan
        
        # Get comprehensive status
        $status = Get-HotspotStatus
        Write-Host "  Active: $($status.IsActive)" -ForegroundColor ($status.IsActive ? 'Green' : 'Red')
        Write-Host "  Connected Devices: $($status.ConnectedDeviceCount)"
        Write-Host "  Network Adapter: $($status.NetworkAdapterName ?? 'Unknown')"
        Write-Host "  IP Address: $($status.IPAddress ?? 'Not assigned')"
        
        if ($i -lt 5) {
            Start-Sleep -Seconds 2
        }
    }
    
    # Final cleanup
    Disable-AdvancedHotspot -Force
    Write-Host "`n✓ Monitoring demo completed" -ForegroundColor Green
} else {
    Write-Host "✗ Failed to start monitoring demo" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== All Examples Completed ===" -ForegroundColor Green
Write-Host ""
Write-Host "Available Commands:" -ForegroundColor Cyan
Write-Host "  Enable-AdvancedHotspot   - Start hotspot with advanced options"
Write-Host "  Disable-AdvancedHotspot  - Stop hotspot safely"
Write-Host "  Get-HotspotInfo          - Get comprehensive hotspot information"
Write-Host "  Manage-HotspotDevice     - Manage connected devices"
Write-Host "  Schedule-HotspotOperation - Schedule start/stop operations"
Write-Host "  Save-HotspotProfile      - Save configuration as profile"
Write-Host "  Load-HotspotProfile      - Load and apply saved profile"
Write-Host ""
Write-Host "For detailed help on any command, use: Get-Help <CommandName> -Detailed" -ForegroundColor Yellow
