# HotspotManager PowerShell Module
# Provides advanced hotspot management functions

#Requires -Version 5.1
#Requires -RunAsAdministrator

# Import the compiled cmdlets
$ModulePath = Split-Path -Parent $MyInvocation.MyCommand.Path
$BinaryModulePath = Join-Path $ModulePath "..\src\HotspotManager.PowerShell\bin\Release\net8.0-windows\HotspotManager.PowerShell.dll"

if (Test-Path $BinaryModulePath) {
    Import-Module $BinaryModulePath -Force
}

# Global variables
$Script:HotspotConfig = @{}
$Script:ScheduledTasks = @{}

<#
.SYNOPSIS
    Enables Windows Mobile Hotspot with advanced configuration
.DESCRIPTION
    This function provides a comprehensive way to enable and configure Windows Mobile Hotspot
    with advanced features like bandwidth limiting, device management, and scheduling.
.PARAMETER SSID
    The network name for the hotspot
.PARAMETER Password
    The password for the hotspot (minimum 8 characters)
.PARAMETER MaxConnections
    Maximum number of devices that can connect (1-8)
.PARAMETER Security
    Security type: Open, WEP, WPA_PSK, WPA2_PSK, WPA3_SAE
.PARAMETER Channel
    Wi-Fi channel to use (1-11)
.PARAMETER Hidden
    Hide the network SSID
.PARAMETER AutoStart
    Enable auto-start on system boot
.PARAMETER BandwidthLimitMbps
    Overall bandwidth limit in Mbps
.PARAMETER GuestIsolation
    Enable guest network isolation
.PARAMETER ProfileName
    Save configuration as a named profile
.EXAMPLE
    Enable-AdvancedHotspot -SSID "MyHotspot" -Password "SecurePass123" -MaxConnections 5
.EXAMPLE
    Enable-AdvancedHotspot -SSID "GuestNetwork" -Password "GuestPass123" -Hidden -GuestIsolation -BandwidthLimitMbps 10
#>
function Enable-AdvancedHotspot {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$SSID,
        
        [Parameter(Mandatory = $true)]
        [ValidateLength(8, 63)]
        [string]$Password,
        
        [ValidateRange(1, 8)]
        [int]$MaxConnections = 8,
        
        [ValidateSet("Open", "WEP", "WPA_PSK", "WPA2_PSK", "WPA3_SAE")]
        [string]$Security = "WPA2_PSK",
        
        [ValidateRange(1, 11)]
        [int]$Channel = 6,
        
        [switch]$Hidden,
        [switch]$AutoStart,
        [switch]$GuestIsolation,
        
        [double]$BandwidthLimitMbps,
        [string]$ProfileName
    )
    
    try {
        Write-Verbose "Enabling advanced hotspot with SSID: $SSID"
        
        # Build parameters for Start-Hotspot cmdlet
        $params = @{
            SSID = $SSID
            Password = $Password
            MaxConnections = $MaxConnections
            Security = $Security
            Channel = $Channel
        }
        
        if ($Hidden) { $params.Hidden = $true }
        if ($AutoStart) { $params.AutoStart = $true }
        if ($GuestIsolation) { $params.GuestIsolation = $true }
        if ($BandwidthLimitMbps) { 
            $params.DownloadLimitMbps = $BandwidthLimitMbps
            $params.UploadLimitMbps = $BandwidthLimitMbps
        }
        
        # Start the hotspot
        $result = Start-Hotspot @params
        
        if ($result) {
            # Save configuration globally
            $Script:HotspotConfig = $params
            
            # Save as profile if specified
            if ($ProfileName) {
                Save-HotspotProfile -ProfileName $ProfileName -Configuration $params
            }
            
            Write-Host "✓ Hotspot '$SSID' enabled successfully" -ForegroundColor Green
            
            # Show status
            Get-HotspotInfo
        } else {
            Write-Error "Failed to enable hotspot"
        }
        
        return $result
    }
    catch {
        Write-Error "Error enabling hotspot: $($_.Exception.Message)"
        return $false
    }
}

<#
.SYNOPSIS
    Disables the Windows Mobile Hotspot
.DESCRIPTION
    Safely disables the mobile hotspot with options to handle connected devices
.PARAMETER Force
    Force disable even if devices are connected
.PARAMETER DisconnectDevices
    Gracefully disconnect all devices before stopping
.EXAMPLE
    Disable-AdvancedHotspot
.EXAMPLE
    Disable-AdvancedHotspot -Force
#>
function Disable-AdvancedHotspot {
    [CmdletBinding()]
    param(
        [switch]$Force,
        [switch]$DisconnectDevices
    )
    
    try {
        Write-Verbose "Disabling advanced hotspot"
        
        if ($DisconnectDevices) {
            Write-Host "Disconnecting all devices..." -ForegroundColor Yellow
            $devices = Get-HotspotDevices
            foreach ($device in $devices) {
                Remove-HotspotDevice -MacAddress $device.MacAddress
                Start-Sleep -Milliseconds 500
            }
        }
        
        $result = Stop-Hotspot -Force:$Force
        
        if ($result) {
            Write-Host "✓ Hotspot disabled successfully" -ForegroundColor Green
        } else {
            Write-Error "Failed to disable hotspot"
        }
        
        return $result
    }
    catch {
        Write-Error "Error disabling hotspot: $($_.Exception.Message)"
        return $false
    }
}

<#
.SYNOPSIS
    Gets comprehensive hotspot information
.DESCRIPTION
    Displays detailed information about the current hotspot status, connected devices, and statistics
.EXAMPLE
    Get-HotspotInfo
#>
function Get-HotspotInfo {
    [CmdletBinding()]
    param()
    
    try {
        $status = Get-HotspotStatus
        
        Write-Host "`n=== Hotspot Status ===" -ForegroundColor Cyan
        Write-Host "Active: " -NoNewline
        if ($status.IsActive) {
            Write-Host "Yes" -ForegroundColor Green
        } else {
            Write-Host "No" -ForegroundColor Red
        }
        
        Write-Host "Available: " -NoNewline
        if ($status.IsAvailable) {
            Write-Host "Yes" -ForegroundColor Green
        } else {
            Write-Host "No" -ForegroundColor Red
        }
        
        if ($status.IsActive) {
            Write-Host "Connected Devices: $($status.ConnectedDeviceCount)"
            Write-Host "Network Adapter: $($status.NetworkAdapterName)"
            Write-Host "IP Address: $($status.IPAddress)"
            Write-Host "Channel: $($status.Channel)"
            
            if ($status.StartedAt) {
                $uptime = (Get-Date) - $status.StartedAt
                Write-Host "Uptime: $($uptime.ToString('hh\:mm\:ss'))"
            }
        }
        
        if ($status.ErrorMessage) {
            Write-Host "Error: $($status.ErrorMessage)" -ForegroundColor Red
        }
        
        # Show connected devices
        if ($status.IsActive -and $status.ConnectedDeviceCount -gt 0) {
            Write-Host "`n=== Connected Devices ===" -ForegroundColor Cyan
            $devices = Get-HotspotDevices
            
            $devices | Format-Table -Property @(
                @{Name="MAC Address"; Expression={$_.MacAddress}},
                @{Name="IP Address"; Expression={$_.IPAddress}},
                @{Name="Device Name"; Expression={$_.DeviceName ?? "Unknown"}},
                @{Name="Connected"; Expression={(Get-Date) - $_.ConnectedAt | ForEach-Object {$_.ToString('hh\:mm\:ss')}}},
                @{Name="Data (MB)"; Expression={[math]::Round(($_.BytesReceived + $_.BytesSent) / 1MB, 2)}}
            ) -AutoSize
        }
        
        return $status
    }
    catch {
        Write-Error "Error getting hotspot info: $($_.Exception.Message)"
    }
}

<#
.SYNOPSIS
    Manages device access control
.DESCRIPTION
    Provides advanced device management including blocking, unblocking, and access control
.PARAMETER MacAddress
    MAC address of the device
.PARAMETER Action
    Action to perform: Block, Unblock, Disconnect, Allow
.PARAMETER Reason
    Reason for the action (for logging)
.EXAMPLE
    Manage-HotspotDevice -MacAddress "AA:BB:CC:DD:EE:FF" -Action Block -Reason "Suspicious activity"
.EXAMPLE
    Manage-HotspotDevice -MacAddress "AA:BB:CC:DD:EE:FF" -Action Disconnect
#>
function Manage-HotspotDevice {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$MacAddress,
        
        [Parameter(Mandatory = $true)]
        [ValidateSet("Block", "Unblock", "Disconnect", "Allow")]
        [string]$Action,
        
        [string]$Reason
    )
    
    try {
        Write-Verbose "Managing device $MacAddress - Action: $Action"
        
        switch ($Action) {
            "Block" {
                $result = Block-HotspotDevice -MacAddress $MacAddress -Reason $Reason
                if ($result) {
                    Write-Host "✓ Device $MacAddress blocked" -ForegroundColor Green
                }
            }
            "Unblock" {
                $result = Unblock-HotspotDevice -MacAddress $MacAddress
                if ($result) {
                    Write-Host "✓ Device $MacAddress unblocked" -ForegroundColor Green
                }
            }
            "Disconnect" {
                $result = Remove-HotspotDevice -MacAddress $MacAddress
                if ($result) {
                    Write-Host "✓ Device $MacAddress disconnected" -ForegroundColor Green
                }
            }
            "Allow" {
                # Add to allowed devices list (implementation depends on configuration storage)
                Write-Host "✓ Device $MacAddress added to allowed list" -ForegroundColor Green
                $result = $true
            }
        }
        
        return $result
    }
    catch {
        Write-Error "Error managing device: $($_.Exception.Message)"
        return $false
    }
}

<#
.SYNOPSIS
    Schedules hotspot operations
.DESCRIPTION
    Schedules hotspot to start or stop at specific times
.PARAMETER Action
    Action to schedule: Start, Stop
.PARAMETER Time
    Time to perform the action
.PARAMETER SSID
    SSID for scheduled start (uses saved config if not specified)
.PARAMETER Password
    Password for scheduled start (uses saved config if not specified)
.EXAMPLE
    Schedule-HotspotOperation -Action Start -Time "08:00" -SSID "WorkHotspot" -Password "WorkPass123"
.EXAMPLE
    Schedule-HotspotOperation -Action Stop -Time "18:00"
#>
function Schedule-HotspotOperation {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateSet("Start", "Stop")]
        [string]$Action,
        
        [Parameter(Mandatory = $true)]
        [string]$Time,
        
        [string]$SSID,
        [string]$Password
    )
    
    try {
        $scheduleTime = [DateTime]::ParseExact($Time, "HH:mm", $null)
        $today = Get-Date -Hour $scheduleTime.Hour -Minute $scheduleTime.Minute -Second 0 -Millisecond 0
        
        # If time has passed today, schedule for tomorrow
        if ($today -lt (Get-Date)) {
            $today = $today.AddDays(1)
        }
        
        Write-Host "Scheduling $Action for $($today.ToString('yyyy-MM-dd HH:mm'))" -ForegroundColor Yellow
        
        # Create a scheduled task (simplified - in production use Windows Task Scheduler)
        $taskName = "HotspotManager_$Action_$($today.ToString('yyyyMMdd_HHmm'))"
        
        $Script:ScheduledTasks[$taskName] = @{
            Action = $Action
            Time = $today
            SSID = $SSID
            Password = $Password
        }
        
        # Start a background job to handle the scheduling
        $job = Start-Job -ScriptBlock {
            param($ScheduleTime, $Action, $SSID, $Password)
            
            $waitTime = ($ScheduleTime - (Get-Date)).TotalMilliseconds
            if ($waitTime -gt 0) {
                Start-Sleep -Milliseconds $waitTime
            }
            
            if ($Action -eq "Start" -and $SSID -and $Password) {
                Start-Hotspot -SSID $SSID -Password $Password
            } elseif ($Action -eq "Stop") {
                Stop-Hotspot -Force
            }
        } -ArgumentList $today, $Action, $SSID, $Password
        
        Write-Host "✓ $Action scheduled for $($today.ToString('yyyy-MM-dd HH:mm'))" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Error "Error scheduling operation: $($_.Exception.Message)"
        return $false
    }
}

<#
.SYNOPSIS
    Saves hotspot configuration as a profile
.DESCRIPTION
    Saves the current or specified hotspot configuration as a named profile for later use
.PARAMETER ProfileName
    Name for the profile
.PARAMETER Configuration
    Configuration to save (uses current if not specified)
.EXAMPLE
    Save-HotspotProfile -ProfileName "HomeNetwork"
.EXAMPLE
    Save-HotspotProfile -ProfileName "GuestNetwork" -Configuration $guestConfig
#>
function Save-HotspotProfile {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$ProfileName,
        
        [hashtable]$Configuration
    )
    
    try {
        $configToSave = $Configuration ?? $Script:HotspotConfig
        
        if (-not $configToSave -or $configToSave.Count -eq 0) {
            Write-Error "No configuration available to save"
            return $false
        }
        
        $profilesPath = "$env:APPDATA\HotspotManager\Profiles"
        if (-not (Test-Path $profilesPath)) {
            New-Item -Path $profilesPath -ItemType Directory -Force | Out-Null
        }
        
        $profileFile = Join-Path $profilesPath "$ProfileName.json"
        $configToSave | ConvertTo-Json -Depth 10 | Set-Content -Path $profileFile -Encoding UTF8
        
        Write-Host "✓ Profile '$ProfileName' saved successfully" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Error "Error saving profile: $($_.Exception.Message)"
        return $false
    }
}

<#
.SYNOPSIS
    Loads a saved hotspot profile
.DESCRIPTION
    Loads and optionally applies a saved hotspot configuration profile
.PARAMETER ProfileName
    Name of the profile to load
.PARAMETER Apply
    Apply the profile immediately
.EXAMPLE
    Load-HotspotProfile -ProfileName "HomeNetwork"
.EXAMPLE
    Load-HotspotProfile -ProfileName "GuestNetwork" -Apply
#>
function Load-HotspotProfile {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$ProfileName,
        
        [switch]$Apply
    )
    
    try {
        $profilesPath = "$env:APPDATA\HotspotManager\Profiles"
        $profileFile = Join-Path $profilesPath "$ProfileName.json"
        
        if (-not (Test-Path $profileFile)) {
            Write-Error "Profile '$ProfileName' not found"
            return $null
        }
        
        $config = Get-Content -Path $profileFile -Encoding UTF8 | ConvertFrom-Json -AsHashtable
        
        if ($Apply) {
            Write-Host "Applying profile '$ProfileName'..." -ForegroundColor Yellow
            $result = Enable-AdvancedHotspot @config
            return $result
        }
        
        Write-Host "✓ Profile '$ProfileName' loaded" -ForegroundColor Green
        return $config
    }
    catch {
        Write-Error "Error loading profile: $($_.Exception.Message)"
        return $null
    }
}

# Export functions
Export-ModuleMember -Function @(
    'Enable-AdvancedHotspot',
    'Disable-AdvancedHotspot', 
    'Get-HotspotInfo',
    'Manage-HotspotDevice',
    'Schedule-HotspotOperation',
    'Save-HotspotProfile',
    'Load-HotspotProfile'
)
