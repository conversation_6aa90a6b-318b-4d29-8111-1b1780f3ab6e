#Requires -Version 5.1
#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Installs and configures the Windows Mobile Hotspot Manager
.DESCRIPTION
    This script installs the HotspotManager application, sets up the PowerShell module,
    and configures the system for optimal hotspot management.
.PARAMETER InstallPath
    The path where the application will be installed
.PARAMETER CreateDesktopShortcut
    Creates a desktop shortcut for the application
.PARAMETER RegisterPowerShellModule
    Registers the PowerShell module in the system
.PARAMETER CheckPrerequisites
    Checks system prerequisites before installation
.EXAMPLE
    .\Install-HotspotManager.ps1 -InstallPath "C:\Program Files\HotspotManager"
.EXAMPLE
    .\Install-HotspotManager.ps1 -CreateDesktopShortcut -RegisterPowerShellModule
#>

param(
    [string]$InstallPath = "$env:ProgramFiles\HotspotManager",
    [switch]$CreateDesktopShortcut,
    [switch]$RegisterPowerShellModule,
    [switch]$CheckPrerequisites = $true
)

# Set error action preference
$ErrorActionPreference = "Stop"

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Function to check prerequisites
function Test-Prerequisites {
    Write-ColorOutput "Checking system prerequisites..." "Yellow"
    
    # Check Windows version
    $osVersion = [System.Environment]::OSVersion.Version
    if ($osVersion.Major -lt 10) {
        throw "Windows 10 or later is required"
    }
    Write-ColorOutput "✓ Windows version: $($osVersion.Major).$($osVersion.Minor)" "Green"
    
    # Check .NET version
    try {
        $dotnetVersion = dotnet --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw ".NET runtime not found"
        }
        Write-ColorOutput "✓ .NET version: $dotnetVersion" "Green"
    }
    catch {
        Write-ColorOutput "✗ .NET 8.0 or later is required. Please install from https://dotnet.microsoft.com/" "Red"
        throw
    }
    
    # Check if running as administrator
    $currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
    if (-not $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
        throw "This script must be run as Administrator"
    }
    Write-ColorOutput "✓ Running as Administrator" "Green"
    
    # Check hosted network support
    try {
        $hostedNetworkSupport = netsh wlan show drivers | Select-String "Hosted network supported"
        if ($hostedNetworkSupport -match "Yes") {
            Write-ColorOutput "✓ Hosted network supported" "Green"
        } else {
            Write-ColorOutput "⚠ Hosted network may not be supported by your wireless adapter" "Yellow"
        }
    }
    catch {
        Write-ColorOutput "⚠ Could not check hosted network support" "Yellow"
    }
    
    # Check wireless adapter
    $wirelessAdapters = Get-NetAdapter | Where-Object { $_.InterfaceDescription -like "*Wireless*" -or $_.InterfaceDescription -like "*Wi-Fi*" }
    if ($wirelessAdapters.Count -gt 0) {
        Write-ColorOutput "✓ Wireless adapter found: $($wirelessAdapters[0].InterfaceDescription)" "Green"
    } else {
        Write-ColorOutput "⚠ No wireless adapter detected" "Yellow"
    }
}

# Function to create installation directory
function New-InstallationDirectory {
    param([string]$Path)
    
    Write-ColorOutput "Creating installation directory: $Path" "Yellow"
    
    if (Test-Path $Path) {
        Write-ColorOutput "Directory already exists, cleaning up..." "Yellow"
        Remove-Item $Path -Recurse -Force
    }
    
    New-Item -Path $Path -ItemType Directory -Force | Out-Null
    Write-ColorOutput "✓ Installation directory created" "Green"
}

# Function to copy application files
function Copy-ApplicationFiles {
    param(
        [string]$SourcePath,
        [string]$DestinationPath
    )
    
    Write-ColorOutput "Copying application files..." "Yellow"
    
    # Find the built application
    $builtApp = Get-ChildItem -Path $SourcePath -Recurse -Name "HotspotManager.UI.exe" | Select-Object -First 1
    if (-not $builtApp) {
        throw "Built application not found. Please build the solution first."
    }
    
    $appDirectory = Split-Path (Join-Path $SourcePath $builtApp) -Parent
    Copy-Item -Path "$appDirectory\*" -Destination $DestinationPath -Recurse -Force
    
    Write-ColorOutput "✓ Application files copied" "Green"
}

# Function to create desktop shortcut
function New-DesktopShortcut {
    param(
        [string]$TargetPath,
        [string]$ShortcutName = "Hotspot Manager"
    )
    
    Write-ColorOutput "Creating desktop shortcut..." "Yellow"
    
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    $shortcutPath = Join-Path $desktopPath "$ShortcutName.lnk"
    
    $shell = New-Object -ComObject WScript.Shell
    $shortcut = $shell.CreateShortcut($shortcutPath)
    $shortcut.TargetPath = $TargetPath
    $shortcut.WorkingDirectory = Split-Path $TargetPath -Parent
    $shortcut.Description = "Windows Mobile Hotspot Manager"
    $shortcut.IconLocation = $TargetPath
    $shortcut.Save()
    
    Write-ColorOutput "✓ Desktop shortcut created" "Green"
}

# Function to register PowerShell module
function Register-PowerShellModule {
    param([string]$ModulePath)
    
    Write-ColorOutput "Registering PowerShell module..." "Yellow"
    
    $moduleSourcePath = Join-Path (Split-Path $PSScriptRoot -Parent) "scripts\HotspotManager.psm1"
    if (-not (Test-Path $moduleSourcePath)) {
        Write-ColorOutput "⚠ PowerShell module not found at $moduleSourcePath" "Yellow"
        return
    }
    
    # Copy module to PowerShell modules directory
    $modulesPath = "$env:ProgramFiles\WindowsPowerShell\Modules\HotspotManager"
    New-Item -Path $modulesPath -ItemType Directory -Force | Out-Null
    Copy-Item -Path $moduleSourcePath -Destination "$modulesPath\HotspotManager.psm1" -Force
    
    # Create module manifest
    $manifestPath = "$modulesPath\HotspotManager.psd1"
    $manifestContent = @"
@{
    ModuleVersion = '1.0.0'
    GUID = '$(New-Guid)'
    Author = 'HotspotManager'
    Description = 'Windows Mobile Hotspot Management Module'
    PowerShellVersion = '5.1'
    RootModule = 'HotspotManager.psm1'
    FunctionsToExport = @(
        'Enable-AdvancedHotspot',
        'Disable-AdvancedHotspot',
        'Get-HotspotInfo',
        'Manage-HotspotDevice',
        'Schedule-HotspotOperation',
        'Save-HotspotProfile',
        'Load-HotspotProfile'
    )
}
"@
    Set-Content -Path $manifestPath -Value $manifestContent -Encoding UTF8
    
    Write-ColorOutput "✓ PowerShell module registered" "Green"
}

# Function to configure Windows features
function Enable-WindowsFeatures {
    Write-ColorOutput "Configuring Windows features..." "Yellow"
    
    try {
        # Enable Internet Connection Sharing service
        Set-Service -Name "SharedAccess" -StartupType Automatic -ErrorAction SilentlyContinue
        Start-Service -Name "SharedAccess" -ErrorAction SilentlyContinue
        Write-ColorOutput "✓ Internet Connection Sharing service enabled" "Green"
        
        # Enable WLAN AutoConfig service
        Set-Service -Name "WlanSvc" -StartupType Automatic -ErrorAction SilentlyContinue
        Start-Service -Name "WlanSvc" -ErrorAction SilentlyContinue
        Write-ColorOutput "✓ WLAN AutoConfig service enabled" "Green"
        
        # Configure firewall rules for hotspot
        $firewallRules = @(
            @{Name="HotspotManager-In"; Direction="Inbound"; Protocol="TCP"; LocalPort="80,443,53"},
            @{Name="HotspotManager-Out"; Direction="Outbound"; Protocol="TCP"; LocalPort="80,443,53"}
        )
        
        foreach ($rule in $firewallRules) {
            try {
                Remove-NetFirewallRule -DisplayName $rule.Name -ErrorAction SilentlyContinue
                New-NetFirewallRule -DisplayName $rule.Name -Direction $rule.Direction -Protocol $rule.Protocol -LocalPort $rule.LocalPort -Action Allow | Out-Null
            }
            catch {
                Write-ColorOutput "⚠ Could not configure firewall rule: $($rule.Name)" "Yellow"
            }
        }
        Write-ColorOutput "✓ Firewall rules configured" "Green"
    }
    catch {
        Write-ColorOutput "⚠ Some Windows features could not be configured: $($_.Exception.Message)" "Yellow"
    }
}

# Function to create uninstaller
function New-Uninstaller {
    param([string]$InstallPath)
    
    Write-ColorOutput "Creating uninstaller..." "Yellow"
    
    $uninstallerContent = @"
#Requires -RunAsAdministrator

Write-Host "Uninstalling HotspotManager..." -ForegroundColor Yellow

# Stop any running instances
Get-Process -Name "HotspotManager.UI" -ErrorAction SilentlyContinue | Stop-Process -Force

# Remove installation directory
if (Test-Path "$InstallPath") {
    Remove-Item "$InstallPath" -Recurse -Force
    Write-Host "✓ Application files removed" -ForegroundColor Green
}

# Remove desktop shortcut
`$shortcutPath = Join-Path ([Environment]::GetFolderPath("Desktop")) "Hotspot Manager.lnk"
if (Test-Path `$shortcutPath) {
    Remove-Item `$shortcutPath -Force
    Write-Host "✓ Desktop shortcut removed" -ForegroundColor Green
}

# Remove PowerShell module
`$modulePath = "`$env:ProgramFiles\WindowsPowerShell\Modules\HotspotManager"
if (Test-Path `$modulePath) {
    Remove-Item `$modulePath -Recurse -Force
    Write-Host "✓ PowerShell module removed" -ForegroundColor Green
}

Write-Host "HotspotManager has been successfully uninstalled." -ForegroundColor Green
"@
    
    $uninstallerPath = Join-Path $InstallPath "Uninstall.ps1"
    Set-Content -Path $uninstallerPath -Value $uninstallerContent -Encoding UTF8
    
    Write-ColorOutput "✓ Uninstaller created" "Green"
}

# Main installation process
try {
    Write-ColorOutput "=== Windows Mobile Hotspot Manager Installation ===" "Cyan"
    Write-ColorOutput "Installation Path: $InstallPath" "White"
    Write-ColorOutput ""
    
    # Check prerequisites
    if ($CheckPrerequisites) {
        Test-Prerequisites
        Write-ColorOutput ""
    }
    
    # Create installation directory
    New-InstallationDirectory -Path $InstallPath
    
    # Copy application files
    $sourcePath = Split-Path $PSScriptRoot -Parent
    Copy-ApplicationFiles -SourcePath $sourcePath -DestinationPath $InstallPath
    
    # Create desktop shortcut
    if ($CreateDesktopShortcut) {
        $exePath = Join-Path $InstallPath "HotspotManager.UI.exe"
        New-DesktopShortcut -TargetPath $exePath
    }
    
    # Register PowerShell module
    if ($RegisterPowerShellModule) {
        Register-PowerShellModule -ModulePath $InstallPath
    }
    
    # Configure Windows features
    Enable-WindowsFeatures
    
    # Create uninstaller
    New-Uninstaller -InstallPath $InstallPath
    
    Write-ColorOutput ""
    Write-ColorOutput "=== Installation Complete ===" "Green"
    Write-ColorOutput "Application installed to: $InstallPath" "White"
    Write-ColorOutput "To start the application, run: $InstallPath\HotspotManager.UI.exe" "White"
    
    if ($RegisterPowerShellModule) {
        Write-ColorOutput "PowerShell module available. Try: Import-Module HotspotManager" "White"
    }
    
    Write-ColorOutput ""
    Write-ColorOutput "Note: The application requires Administrator privileges to manage hotspot functionality." "Yellow"
}
catch {
    Write-ColorOutput ""
    Write-ColorOutput "Installation failed: $($_.Exception.Message)" "Red"
    exit 1
}
