using System.ComponentModel.DataAnnotations;

namespace HotspotManager.Core.Models;

/// <summary>
/// Configuration model for hotspot settings
/// </summary>
public class HotspotConfiguration
{
    [Required]
    [StringLength(32, MinimumLength = 1)]
    public string SSID { get; set; } = "Windows-Hotspot";

    [Required]
    [StringLength(63, MinimumLength = 8)]
    public string Password { get; set; } = string.Empty;

    [Range(1, 8)]
    public int MaxConnections { get; set; } = 8;

    public SecurityType Security { get; set; } = SecurityType.WPA2_PSK;

    public int Channel { get; set; } = 6;

    public bool IsHidden { get; set; } = false;

    public bool AutoStart { get; set; } = false;

    public TimeSpan? AutoStopAfter { get; set; }

    public BandwidthLimits? BandwidthLimits { get; set; }

    public List<string> AllowedDevices { get; set; } = new();

    public List<string> BlockedDevices { get; set; } = new();

    public bool GuestNetworkIsolation { get; set; } = true;

    public DhcpConfiguration? DhcpConfig { get; set; }

    public DnsConfiguration? DnsConfig { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime? LastModified { get; set; }

    public string? ProfileName { get; set; }

    public bool IsDefault { get; set; } = false;
}

public enum SecurityType
{
    Open = 0,
    WEP = 1,
    WPA_PSK = 2,
    WPA2_PSK = 3,
    WPA3_SAE = 4
}

public class BandwidthLimits
{
    /// <summary>
    /// Download speed limit in Mbps (0 = unlimited)
    /// </summary>
    public double DownloadLimitMbps { get; set; } = 0;

    /// <summary>
    /// Upload speed limit in Mbps (0 = unlimited)
    /// </summary>
    public double UploadLimitMbps { get; set; } = 0;

    /// <summary>
    /// Per-device download limit in Mbps (0 = unlimited)
    /// </summary>
    public double PerDeviceDownloadLimitMbps { get; set; } = 0;

    /// <summary>
    /// Per-device upload limit in Mbps (0 = unlimited)
    /// </summary>
    public double PerDeviceUploadLimitMbps { get; set; } = 0;

    /// <summary>
    /// Data usage limit in GB per day (0 = unlimited)
    /// </summary>
    public double DailyDataLimitGB { get; set; } = 0;

    /// <summary>
    /// Data usage limit in GB per month (0 = unlimited)
    /// </summary>
    public double MonthlyDataLimitGB { get; set; } = 0;
}

public class DhcpConfiguration
{
    public string StartIP { get; set; } = "*************";
    public string EndIP { get; set; } = "*************54";
    public string SubnetMask { get; set; } = "*************";
    public string Gateway { get; set; } = "*************";
    public TimeSpan LeaseTime { get; set; } = TimeSpan.FromHours(24);
    public List<DhcpReservation> Reservations { get; set; } = new();
}

public class DhcpReservation
{
    public string MacAddress { get; set; } = string.Empty;
    public string IPAddress { get; set; } = string.Empty;
    public string? DeviceName { get; set; }
}

public class DnsConfiguration
{
    public string PrimaryDNS { get; set; } = "*******";
    public string SecondaryDNS { get; set; } = "*******";
    public bool EnableDnsFiltering { get; set; } = false;
    public List<string> BlockedDomains { get; set; } = new();
    public List<string> AllowedDomains { get; set; } = new();
    public bool LogDnsQueries { get; set; } = false;
}

/// <summary>
/// Connected device information
/// </summary>
public class ConnectedDevice
{
    public string MacAddress { get; set; } = string.Empty;
    public string? DeviceName { get; set; }
    public string IPAddress { get; set; } = string.Empty;
    public DateTime ConnectedAt { get; set; }
    public DateTime LastSeen { get; set; }
    public long BytesReceived { get; set; }
    public long BytesSent { get; set; }
    public double CurrentDownloadSpeedMbps { get; set; }
    public double CurrentUploadSpeedMbps { get; set; }
    public bool IsBlocked { get; set; }
    public string? Manufacturer { get; set; }
    public DeviceType DeviceType { get; set; } = DeviceType.Unknown;
}

public enum DeviceType
{
    Unknown = 0,
    Phone = 1,
    Tablet = 2,
    Laptop = 3,
    Desktop = 4,
    SmartTV = 5,
    GameConsole = 6,
    IoTDevice = 7
}

/// <summary>
/// Hotspot status information
/// </summary>
public class HotspotStatus
{
    public bool IsActive { get; set; }
    public bool IsAvailable { get; set; }
    public string? ErrorMessage { get; set; }
    public DateTime? StartedAt { get; set; }
    public int ConnectedDeviceCount { get; set; }
    public List<ConnectedDevice> ConnectedDevices { get; set; } = new();
    public NetworkStatistics Statistics { get; set; } = new();
    public string? NetworkAdapterName { get; set; }
    public string? IPAddress { get; set; }
    public int? Channel { get; set; }
    public int? SignalStrength { get; set; }
}

public class NetworkStatistics
{
    public long TotalBytesReceived { get; set; }
    public long TotalBytesSent { get; set; }
    public double CurrentDownloadSpeedMbps { get; set; }
    public double CurrentUploadSpeedMbps { get; set; }
    public double PeakDownloadSpeedMbps { get; set; }
    public double PeakUploadSpeedMbps { get; set; }
    public DateTime LastUpdated { get; set; }
    public TimeSpan Uptime { get; set; }
}

/// <summary>
/// Hotspot event information
/// </summary>
public class HotspotEvent
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public HotspotEventType EventType { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? DeviceMacAddress { get; set; }
    public string? DeviceName { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
}

public enum HotspotEventType
{
    HotspotStarted = 1,
    HotspotStopped = 2,
    DeviceConnected = 3,
    DeviceDisconnected = 4,
    DeviceBlocked = 5,
    BandwidthLimitExceeded = 6,
    DataLimitExceeded = 7,
    SecurityViolation = 8,
    ConfigurationChanged = 9,
    Error = 10,
    Warning = 11,
    Information = 12
}
