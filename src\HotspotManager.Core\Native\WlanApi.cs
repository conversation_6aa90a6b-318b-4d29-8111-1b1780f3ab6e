using System.Runtime.InteropServices;
using System.Text;

namespace HotspotManager.Core.Native;

/// <summary>
/// Windows WLAN API wrapper for hosted network functionality
/// </summary>
public static class WlanApi
{
    private const string WLANAPI_DLL = "wlanapi.dll";

    #region Enums

    public enum WLAN_HOSTED_NETWORK_STATE
    {
        wlan_hosted_network_unavailable,
        wlan_hosted_network_idle,
        wlan_hosted_network_active
    }

    public enum WLAN_HOSTED_NETWORK_REASON
    {
        wlan_hosted_network_reason_success = 0,
        wlan_hosted_network_reason_unspecified,
        wlan_hosted_network_reason_bad_parameters,
        wlan_hosted_network_reason_service_shutting_down,
        wlan_hosted_network_reason_insufficient_resources,
        wlan_hosted_network_reason_elevation_required,
        wlan_hosted_network_reason_read_only,
        wlan_hosted_network_reason_persistence_failed,
        wlan_hosted_network_reason_crypt_error,
        wlan_hosted_network_reason_impersonation,
        wlan_hosted_network_reason_stop_before_start,
        wlan_hosted_network_reason_interface_available,
        wlan_hosted_network_reason_interface_unavailable,
        wlan_hosted_network_reason_miniport_stopped,
        wlan_hosted_network_reason_miniport_started,
        wlan_hosted_network_reason_incompatible_connection_started,
        wlan_hosted_network_reason_incompatible_connection_stopped,
        wlan_hosted_network_reason_user_action,
        wlan_hosted_network_reason_client_abort,
        wlan_hosted_network_reason_ap_start_failed,
        wlan_hosted_network_reason_peer_arrived,
        wlan_hosted_network_reason_peer_departed,
        wlan_hosted_network_reason_peer_timeout,
        wlan_hosted_network_reason_gp_denied,
        wlan_hosted_network_reason_service_unavailable,
        wlan_hosted_network_reason_device_change,
        wlan_hosted_network_reason_properties_change,
        wlan_hosted_network_reason_virtual_station_blocking_use,
        wlan_hosted_network_reason_service_available_on_virtual_station
    }

    public enum WLAN_HOSTED_NETWORK_OPCODE
    {
        wlan_hosted_network_opcode_connection_settings,
        wlan_hosted_network_opcode_security_settings,
        wlan_hosted_network_opcode_station_profile,
        wlan_hosted_network_opcode_enable
    }

    public enum DOT11_AUTH_ALGORITHM : uint
    {
        DOT11_AUTH_ALGO_80211_OPEN = 1,
        DOT11_AUTH_ALGO_80211_SHARED_KEY = 2,
        DOT11_AUTH_ALGO_WPA = 3,
        DOT11_AUTH_ALGO_WPA_PSK = 4,
        DOT11_AUTH_ALGO_WPA_NONE = 5,
        DOT11_AUTH_ALGO_RSNA = 6,
        DOT11_AUTH_ALGO_RSNA_PSK = 7,
        DOT11_AUTH_ALGO_WPA3 = 8,
        DOT11_AUTH_ALGO_WPA3_SAE = 9
    }

    public enum DOT11_CIPHER_ALGORITHM : uint
    {
        DOT11_CIPHER_ALGO_NONE = 0x00,
        DOT11_CIPHER_ALGO_WEP40 = 0x01,
        DOT11_CIPHER_ALGO_TKIP = 0x02,
        DOT11_CIPHER_ALGO_CCMP = 0x04,
        DOT11_CIPHER_ALGO_WEP104 = 0x05,
        DOT11_CIPHER_ALGO_WPA_USE_GROUP = 0x100,
        DOT11_CIPHER_ALGO_RSN_USE_GROUP = 0x100,
        DOT11_CIPHER_ALGO_WEP = 0x101,
        DOT11_CIPHER_ALGO_IHV_START = 0x80000000,
        DOT11_CIPHER_ALGO_IHV_END = 0xffffffff
    }

    #endregion

    #region Structures

    [StructLayout(LayoutKind.Sequential)]
    public struct WLAN_HOSTED_NETWORK_CONNECTION_SETTINGS
    {
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 32)]
        public byte[] hostedNetworkSSID;
        public uint dwMaxNumberOfPeers;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct WLAN_HOSTED_NETWORK_SECURITY_SETTINGS
    {
        public DOT11_AUTH_ALGORITHM dot11AuthAlgo;
        public DOT11_CIPHER_ALGORITHM dot11CipherAlgo;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct WLAN_HOSTED_NETWORK_STATUS
    {
        public WLAN_HOSTED_NETWORK_STATE HostedNetworkState;
        public Guid IPDeviceID;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 6)]
        public byte[] wlanHostedNetworkBSSID;
        public DOT11_PHY_TYPE dot11PhyType;
        public uint ulChannelFrequency;
        public uint dwNumberOfPeers;
        public IntPtr PeerList;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct WLAN_HOSTED_NETWORK_PEER_STATE
    {
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 6)]
        public byte[] PeerMacAddress;
        public WLAN_HOSTED_NETWORK_PEER_AUTH_STATE PeerAuthState;
    }

    public enum WLAN_HOSTED_NETWORK_PEER_AUTH_STATE
    {
        wlan_hosted_network_peer_state_invalid,
        wlan_hosted_network_peer_state_authenticated
    }

    public enum DOT11_PHY_TYPE : uint
    {
        dot11_phy_type_unknown = 0,
        dot11_phy_type_any = 0,
        dot11_phy_type_fhss = 1,
        dot11_phy_type_dsss = 2,
        dot11_phy_type_irbaseband = 3,
        dot11_phy_type_ofdm = 4,
        dot11_phy_type_hrdsss = 5,
        dot11_phy_type_erp = 6,
        dot11_phy_type_ht = 7,
        dot11_phy_type_vht = 8,
        dot11_phy_type_he = 9,
        dot11_phy_type_IHV_start = 0x80000000,
        dot11_phy_type_IHV_end = 0xffffffff
    }

    #endregion

    #region API Functions

    [DllImport(WLANAPI_DLL, SetLastError = true)]
    public static extern uint WlanOpenHandle(
        uint dwClientVersion,
        IntPtr pReserved,
        out uint pdwNegotiatedVersion,
        out IntPtr phClientHandle);

    [DllImport(WLANAPI_DLL, SetLastError = true)]
    public static extern uint WlanCloseHandle(
        IntPtr hClientHandle,
        IntPtr pReserved);

    [DllImport(WLANAPI_DLL, SetLastError = true)]
    public static extern uint WlanHostedNetworkQueryProperty(
        IntPtr hClientHandle,
        WLAN_HOSTED_NETWORK_OPCODE OpCode,
        out uint pdwDataSize,
        out IntPtr ppvData,
        out WLAN_OPCODE_VALUE_TYPE pWlanOpcodeValueType,
        IntPtr pReserved);

    [DllImport(WLANAPI_DLL, SetLastError = true)]
    public static extern uint WlanHostedNetworkSetProperty(
        IntPtr hClientHandle,
        WLAN_HOSTED_NETWORK_OPCODE OpCode,
        uint dwDataSize,
        IntPtr pvData,
        out WLAN_HOSTED_NETWORK_REASON pFailReason,
        IntPtr pReserved);

    [DllImport(WLANAPI_DLL, SetLastError = true)]
    public static extern uint WlanHostedNetworkInitSettings(
        IntPtr hClientHandle,
        out WLAN_HOSTED_NETWORK_REASON pFailReason,
        IntPtr pReserved);

    [DllImport(WLANAPI_DLL, SetLastError = true)]
    public static extern uint WlanHostedNetworkStartUsing(
        IntPtr hClientHandle,
        out WLAN_HOSTED_NETWORK_REASON pFailReason,
        IntPtr pReserved);

    [DllImport(WLANAPI_DLL, SetLastError = true)]
    public static extern uint WlanHostedNetworkStopUsing(
        IntPtr hClientHandle,
        out WLAN_HOSTED_NETWORK_REASON pFailReason,
        IntPtr pReserved);

    [DllImport(WLANAPI_DLL, SetLastError = true)]
    public static extern uint WlanHostedNetworkForceStart(
        IntPtr hClientHandle,
        out WLAN_HOSTED_NETWORK_REASON pFailReason,
        IntPtr pReserved);

    [DllImport(WLANAPI_DLL, SetLastError = true)]
    public static extern uint WlanHostedNetworkForceStop(
        IntPtr hClientHandle,
        out WLAN_HOSTED_NETWORK_REASON pFailReason,
        IntPtr pReserved);

    [DllImport(WLANAPI_DLL, SetLastError = true)]
    public static extern uint WlanHostedNetworkQueryStatus(
        IntPtr hClientHandle,
        out IntPtr ppWlanHostedNetworkStatus,
        IntPtr pReserved);

    [DllImport(WLANAPI_DLL, SetLastError = true)]
    public static extern uint WlanHostedNetworkSetSecondaryKey(
        IntPtr hClientHandle,
        uint dwKeyLength,
        byte[] pucKeyData,
        bool bIsPassPhrase,
        bool bPersistent,
        out WLAN_HOSTED_NETWORK_REASON pFailReason,
        IntPtr pReserved);

    [DllImport(WLANAPI_DLL, SetLastError = true)]
    public static extern uint WlanHostedNetworkQuerySecondaryKey(
        IntPtr hClientHandle,
        out uint pdwKeyLength,
        out IntPtr ppucKeyData,
        out bool pbIsPassPhrase,
        out bool pbPersistent,
        out WLAN_HOSTED_NETWORK_REASON pFailReason,
        IntPtr pReserved);

    [DllImport(WLANAPI_DLL, SetLastError = true)]
    public static extern void WlanFreeMemory(IntPtr pMemory);

    public enum WLAN_OPCODE_VALUE_TYPE
    {
        wlan_opcode_value_type_query_only,
        wlan_opcode_value_type_set_by_group_policy,
        wlan_opcode_value_type_set_by_user,
        wlan_opcode_value_type_invalid
    }

    #endregion

    #region Constants

    public const uint WLAN_API_VERSION_2_0 = 2;
    public const uint ERROR_SUCCESS = 0;
    public const uint ERROR_INVALID_PARAMETER = 87;
    public const uint ERROR_INVALID_HANDLE = 6;
    public const uint ERROR_NOT_SUPPORTED = 50;
    public const uint ERROR_SERVICE_NOT_ACTIVE = 1062;

    #endregion
}
