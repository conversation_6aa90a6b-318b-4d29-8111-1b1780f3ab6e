using HotspotManager.Core.Models;

namespace HotspotManager.Core.Services;

/// <summary>
/// Interface for hotspot management operations
/// </summary>
public interface IHotspotService
{
    /// <summary>
    /// Event fired when hotspot status changes
    /// </summary>
    event EventHandler<HotspotStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// Event fired when a device connects or disconnects
    /// </summary>
    event EventHandler<DeviceConnectionEventArgs>? DeviceConnectionChanged;

    /// <summary>
    /// Event fired when network statistics are updated
    /// </summary>
    event EventHandler<NetworkStatisticsEventArgs>? StatisticsUpdated;

    /// <summary>
    /// Event fired when a hotspot event occurs
    /// </summary>
    event EventHandler<HotspotEventArgs>? HotspotEventOccurred;

    /// <summary>
    /// Check if hotspot functionality is available on this system
    /// </summary>
    Task<bool> IsHotspotAvailableAsync();

    /// <summary>
    /// Get current hotspot status
    /// </summary>
    Task<HotspotStatus> GetStatusAsync();

    /// <summary>
    /// Start the hotspot with specified configuration
    /// </summary>
    Task<bool> StartHotspotAsync(HotspotConfiguration config);

    /// <summary>
    /// Stop the hotspot
    /// </summary>
    Task<bool> StopHotspotAsync();

    /// <summary>
    /// Restart the hotspot with current or new configuration
    /// </summary>
    Task<bool> RestartHotspotAsync(HotspotConfiguration? newConfig = null);

    /// <summary>
    /// Update hotspot configuration (may require restart)
    /// </summary>
    Task<bool> UpdateConfigurationAsync(HotspotConfiguration config);

    /// <summary>
    /// Get current hotspot configuration
    /// </summary>
    Task<HotspotConfiguration?> GetConfigurationAsync();

    /// <summary>
    /// Get list of connected devices
    /// </summary>
    Task<List<ConnectedDevice>> GetConnectedDevicesAsync();

    /// <summary>
    /// Disconnect a specific device
    /// </summary>
    Task<bool> DisconnectDeviceAsync(string macAddress);

    /// <summary>
    /// Block a device from connecting
    /// </summary>
    Task<bool> BlockDeviceAsync(string macAddress, string? reason = null);

    /// <summary>
    /// Unblock a previously blocked device
    /// </summary>
    Task<bool> UnblockDeviceAsync(string macAddress);

    /// <summary>
    /// Get network statistics
    /// </summary>
    Task<NetworkStatistics> GetNetworkStatisticsAsync();

    /// <summary>
    /// Set bandwidth limits for the hotspot
    /// </summary>
    Task<bool> SetBandwidthLimitsAsync(BandwidthLimits limits);

    /// <summary>
    /// Set bandwidth limits for a specific device
    /// </summary>
    Task<bool> SetDeviceBandwidthLimitsAsync(string macAddress, BandwidthLimits limits);

    /// <summary>
    /// Get hotspot events within a time range
    /// </summary>
    Task<List<HotspotEvent>> GetEventsAsync(DateTime? startTime = null, DateTime? endTime = null, HotspotEventType? eventType = null);

    /// <summary>
    /// Clear hotspot event log
    /// </summary>
    Task<bool> ClearEventLogAsync();

    /// <summary>
    /// Save configuration profile
    /// </summary>
    Task<bool> SaveProfileAsync(HotspotConfiguration config, string profileName);

    /// <summary>
    /// Load configuration profile
    /// </summary>
    Task<HotspotConfiguration?> LoadProfileAsync(string profileName);

    /// <summary>
    /// Get list of saved profiles
    /// </summary>
    Task<List<string>> GetProfileNamesAsync();

    /// <summary>
    /// Delete a configuration profile
    /// </summary>
    Task<bool> DeleteProfileAsync(string profileName);

    /// <summary>
    /// Schedule hotspot to start at specified time
    /// </summary>
    Task<bool> ScheduleStartAsync(DateTime startTime, HotspotConfiguration? config = null);

    /// <summary>
    /// Schedule hotspot to stop at specified time
    /// </summary>
    Task<bool> ScheduleStopAsync(DateTime stopTime);

    /// <summary>
    /// Cancel scheduled start/stop
    /// </summary>
    Task<bool> CancelScheduledOperationsAsync();

    /// <summary>
    /// Get available network adapters that can be used for hotspot
    /// </summary>
    Task<List<NetworkAdapter>> GetAvailableAdaptersAsync();

    /// <summary>
    /// Set the network adapter to use for hotspot
    /// </summary>
    Task<bool> SetNetworkAdapterAsync(string adapterId);

    /// <summary>
    /// Test network connectivity
    /// </summary>
    Task<bool> TestConnectivityAsync();

    /// <summary>
    /// Export configuration and logs
    /// </summary>
    Task<bool> ExportDataAsync(string filePath, bool includeEvents = true, bool includeStatistics = true);

    /// <summary>
    /// Import configuration
    /// </summary>
    Task<bool> ImportConfigurationAsync(string filePath);
}

/// <summary>
/// Event arguments for hotspot status changes
/// </summary>
public class HotspotStatusChangedEventArgs : EventArgs
{
    public HotspotStatus Status { get; }
    public HotspotStatus? PreviousStatus { get; }

    public HotspotStatusChangedEventArgs(HotspotStatus status, HotspotStatus? previousStatus = null)
    {
        Status = status;
        PreviousStatus = previousStatus;
    }
}

/// <summary>
/// Event arguments for device connection changes
/// </summary>
public class DeviceConnectionEventArgs : EventArgs
{
    public ConnectedDevice Device { get; }
    public bool IsConnected { get; }

    public DeviceConnectionEventArgs(ConnectedDevice device, bool isConnected)
    {
        Device = device;
        IsConnected = isConnected;
    }
}

/// <summary>
/// Event arguments for network statistics updates
/// </summary>
public class NetworkStatisticsEventArgs : EventArgs
{
    public NetworkStatistics Statistics { get; }

    public NetworkStatisticsEventArgs(NetworkStatistics statistics)
    {
        Statistics = statistics;
    }
}

/// <summary>
/// Event arguments for hotspot events
/// </summary>
public class HotspotEventArgs : EventArgs
{
    public HotspotEvent Event { get; }

    public HotspotEventArgs(HotspotEvent hotspotEvent)
    {
        Event = hotspotEvent;
    }
}

/// <summary>
/// Network adapter information
/// </summary>
public class NetworkAdapter
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string MacAddress { get; set; } = string.Empty;
    public bool IsWireless { get; set; }
    public bool IsConnected { get; set; }
    public bool SupportsHostedNetwork { get; set; }
    public string? IPAddress { get; set; }
    public AdapterType Type { get; set; }
}

public enum AdapterType
{
    Unknown = 0,
    Ethernet = 1,
    Wireless = 2,
    Virtual = 3,
    Bluetooth = 4
}
