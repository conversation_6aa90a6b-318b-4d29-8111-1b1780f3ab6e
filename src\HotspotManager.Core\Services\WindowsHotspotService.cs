using System.Runtime.InteropServices;
using System.Text;
using System.Net.NetworkInformation;
using Microsoft.Extensions.Logging;
using HotspotManager.Core.Models;
using HotspotManager.Core.Native;
using System.Management;
using System.Net;
using System.Text.Json;
using System.IO;

namespace HotspotManager.Core.Services;

/// <summary>
/// Windows implementation of hotspot service using WLAN API
/// </summary>
public class WindowsHotspotService : IHotspotService, IDisposable
{
    private readonly ILogger<WindowsHotspotService> _logger;
    private readonly System.Threading.Timer _statusTimer;
    private readonly System.Threading.Timer _statisticsTimer;
    private IntPtr _wlanHandle = IntPtr.Zero;
    private HotspotStatus _currentStatus = new();
    private HotspotConfiguration? _currentConfig;
    private readonly List<HotspotEvent> _events = new();
    private readonly Dictionary<string, ConnectedDevice> _connectedDevices = new();
    private bool _disposed = false;

    public event EventHandler<HotspotStatusChangedEventArgs>? StatusChanged;
    public event EventHandler<DeviceConnectionEventArgs>? DeviceConnectionChanged;
    public event EventHandler<NetworkStatisticsEventArgs>? StatisticsUpdated;
    public event EventHandler<HotspotEventArgs>? HotspotEventOccurred;

    public WindowsHotspotService(ILogger<WindowsHotspotService> logger)
    {
        _logger = logger;
        
        // Initialize WLAN handle
        InitializeWlanHandle();
        
        // Start monitoring timers
        _statusTimer = new System.Threading.Timer(CheckStatusCallback, null, TimeSpan.Zero, TimeSpan.FromSeconds(5));
        _statisticsTimer = new System.Threading.Timer(UpdateStatisticsCallback, null, TimeSpan.Zero, TimeSpan.FromSeconds(2));
    }

    private void InitializeWlanHandle()
    {
        try
        {
            uint negotiatedVersion;
            var result = WlanApi.WlanOpenHandle(
                WlanApi.WLAN_API_VERSION_2_0,
                IntPtr.Zero,
                out negotiatedVersion,
                out _wlanHandle);

            if (result != WlanApi.ERROR_SUCCESS)
            {
                _logger.LogError("Failed to open WLAN handle. Error: {Error}", result);
                throw new InvalidOperationException($"Failed to initialize WLAN API. Error: {result}");
            }

            _logger.LogInformation("WLAN handle initialized successfully. Version: {Version}", negotiatedVersion);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing WLAN handle");
            throw;
        }
    }

    public async Task<bool> IsHotspotAvailableAsync()
    {
        try
        {
            if (_wlanHandle == IntPtr.Zero)
                return false;

            // Check if hosted network is supported
            var result = WlanApi.WlanHostedNetworkQueryProperty(
                _wlanHandle,
                WlanApi.WLAN_HOSTED_NETWORK_OPCODE.wlan_hosted_network_opcode_enable,
                out uint dataSize,
                out IntPtr data,
                out WlanApi.WLAN_OPCODE_VALUE_TYPE valueType,
                IntPtr.Zero);

            if (data != IntPtr.Zero)
                WlanApi.WlanFreeMemory(data);

            return result == WlanApi.ERROR_SUCCESS;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking hotspot availability");
            return false;
        }
    }

    public async Task<HotspotStatus> GetStatusAsync()
    {
        try
        {
            if (_wlanHandle == IntPtr.Zero)
            {
                return new HotspotStatus
                {
                    IsActive = false,
                    IsAvailable = false,
                    ErrorMessage = "WLAN API not initialized"
                };
            }

            var result = WlanApi.WlanHostedNetworkQueryStatus(
                _wlanHandle,
                out IntPtr statusPtr,
                IntPtr.Zero);

            if (result != WlanApi.ERROR_SUCCESS)
            {
                return new HotspotStatus
                {
                    IsActive = false,
                    IsAvailable = false,
                    ErrorMessage = $"Failed to query status. Error: {result}"
                };
            }

            try
            {
                var status = Marshal.PtrToStructure<WlanApi.WLAN_HOSTED_NETWORK_STATUS>(statusPtr);
                
                var hotspotStatus = new HotspotStatus
                {
                    IsActive = status.HostedNetworkState == WlanApi.WLAN_HOSTED_NETWORK_STATE.wlan_hosted_network_active,
                    IsAvailable = status.HostedNetworkState != WlanApi.WLAN_HOSTED_NETWORK_STATE.wlan_hosted_network_unavailable,
                    ConnectedDeviceCount = (int)status.dwNumberOfPeers,
                    Channel = GetChannelFromFrequency(status.ulChannelFrequency),
                    NetworkAdapterName = GetNetworkAdapterName(status.IPDeviceID)
                };

                if (hotspotStatus.IsActive && _currentConfig != null)
                {
                    hotspotStatus.StartedAt = DateTime.UtcNow; // This should be tracked properly
                }

                // Update connected devices
                await UpdateConnectedDevicesAsync(status);

                return hotspotStatus;
            }
            finally
            {
                WlanApi.WlanFreeMemory(statusPtr);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting hotspot status");
            return new HotspotStatus
            {
                IsActive = false,
                IsAvailable = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<bool> StartHotspotAsync(HotspotConfiguration config)
    {
        try
        {
            if (_wlanHandle == IntPtr.Zero)
            {
                _logger.LogError("WLAN handle not initialized");
                return false;
            }

            // Validate configuration
            if (string.IsNullOrEmpty(config.SSID) || string.IsNullOrEmpty(config.Password))
            {
                _logger.LogError("Invalid configuration: SSID and Password are required");
                return false;
            }

            // Set connection settings
            if (!await SetConnectionSettingsAsync(config))
            {
                _logger.LogError("Failed to set connection settings");
                return false;
            }

            // Set security settings
            if (!await SetSecuritySettingsAsync(config))
            {
                _logger.LogError("Failed to set security settings");
                return false;
            }

            // Set password
            if (!await SetPasswordAsync(config.Password))
            {
                _logger.LogError("Failed to set password");
                return false;
            }

            // Enable hosted network
            if (!await EnableHostedNetworkAsync(true))
            {
                _logger.LogError("Failed to enable hosted network");
                return false;
            }

            // Start hosted network
            var result = WlanApi.WlanHostedNetworkStartUsing(
                _wlanHandle,
                out WlanApi.WLAN_HOSTED_NETWORK_REASON failReason,
                IntPtr.Zero);

            if (result != WlanApi.ERROR_SUCCESS)
            {
                _logger.LogError("Failed to start hosted network. Error: {Error}, Reason: {Reason}", result, failReason);
                return false;
            }

            _currentConfig = config;
            
            // Log event
            await LogEventAsync(new HotspotEvent
            {
                EventType = HotspotEventType.HotspotStarted,
                Message = $"Hotspot started with SSID: {config.SSID}"
            });

            _logger.LogInformation("Hotspot started successfully with SSID: {SSID}", config.SSID);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting hotspot");
            await LogEventAsync(new HotspotEvent
            {
                EventType = HotspotEventType.Error,
                Message = $"Failed to start hotspot: {ex.Message}"
            });
            return false;
        }
    }

    public async Task<bool> StopHotspotAsync()
    {
        try
        {
            if (_wlanHandle == IntPtr.Zero)
            {
                _logger.LogError("WLAN handle not initialized");
                return false;
            }

            var result = WlanApi.WlanHostedNetworkStopUsing(
                _wlanHandle,
                out WlanApi.WLAN_HOSTED_NETWORK_REASON failReason,
                IntPtr.Zero);

            if (result != WlanApi.ERROR_SUCCESS)
            {
                _logger.LogError("Failed to stop hosted network. Error: {Error}, Reason: {Reason}", result, failReason);
                return false;
            }

            // Disable hosted network
            await EnableHostedNetworkAsync(false);

            _currentConfig = null;
            _connectedDevices.Clear();

            // Log event
            await LogEventAsync(new HotspotEvent
            {
                EventType = HotspotEventType.HotspotStopped,
                Message = "Hotspot stopped"
            });

            _logger.LogInformation("Hotspot stopped successfully");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping hotspot");
            await LogEventAsync(new HotspotEvent
            {
                EventType = HotspotEventType.Error,
                Message = $"Failed to stop hotspot: {ex.Message}"
            });
            return false;
        }
    }

    public async Task<bool> RestartHotspotAsync(HotspotConfiguration? newConfig = null)
    {
        var config = newConfig ?? _currentConfig;
        if (config == null)
        {
            _logger.LogError("No configuration available for restart");
            return false;
        }

        var stopResult = await StopHotspotAsync();
        if (!stopResult)
        {
            _logger.LogError("Failed to stop hotspot during restart");
            return false;
        }

        // Wait a moment for cleanup
        await Task.Delay(2000);

        return await StartHotspotAsync(config);
    }

    private async Task<bool> SetConnectionSettingsAsync(HotspotConfiguration config)
    {
        try
        {
            var settings = new WlanApi.WLAN_HOSTED_NETWORK_CONNECTION_SETTINGS
            {
                hostedNetworkSSID = new byte[32],
                dwMaxNumberOfPeers = (uint)config.MaxConnections
            };

            var ssidBytes = Encoding.UTF8.GetBytes(config.SSID);
            Array.Copy(ssidBytes, settings.hostedNetworkSSID, Math.Min(ssidBytes.Length, 32));

            var settingsPtr = Marshal.AllocHGlobal(Marshal.SizeOf(settings));
            try
            {
                Marshal.StructureToPtr(settings, settingsPtr, false);

                var result = WlanApi.WlanHostedNetworkSetProperty(
                    _wlanHandle,
                    WlanApi.WLAN_HOSTED_NETWORK_OPCODE.wlan_hosted_network_opcode_connection_settings,
                    (uint)Marshal.SizeOf(settings),
                    settingsPtr,
                    out WlanApi.WLAN_HOSTED_NETWORK_REASON failReason,
                    IntPtr.Zero);

                return result == WlanApi.ERROR_SUCCESS;
            }
            finally
            {
                Marshal.FreeHGlobal(settingsPtr);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting connection settings");
            return false;
        }
    }

    private async Task<bool> SetSecuritySettingsAsync(HotspotConfiguration config)
    {
        try
        {
            var authAlgo = config.Security switch
            {
                SecurityType.Open => WlanApi.DOT11_AUTH_ALGORITHM.DOT11_AUTH_ALGO_80211_OPEN,
                SecurityType.WEP => WlanApi.DOT11_AUTH_ALGORITHM.DOT11_AUTH_ALGO_80211_SHARED_KEY,
                SecurityType.WPA_PSK => WlanApi.DOT11_AUTH_ALGORITHM.DOT11_AUTH_ALGO_WPA_PSK,
                SecurityType.WPA2_PSK => WlanApi.DOT11_AUTH_ALGORITHM.DOT11_AUTH_ALGO_RSNA_PSK,
                SecurityType.WPA3_SAE => WlanApi.DOT11_AUTH_ALGORITHM.DOT11_AUTH_ALGO_WPA3_SAE,
                _ => WlanApi.DOT11_AUTH_ALGORITHM.DOT11_AUTH_ALGO_RSNA_PSK
            };

            var cipherAlgo = config.Security switch
            {
                SecurityType.Open => WlanApi.DOT11_CIPHER_ALGORITHM.DOT11_CIPHER_ALGO_NONE,
                SecurityType.WEP => WlanApi.DOT11_CIPHER_ALGORITHM.DOT11_CIPHER_ALGO_WEP,
                SecurityType.WPA_PSK => WlanApi.DOT11_CIPHER_ALGORITHM.DOT11_CIPHER_ALGO_TKIP,
                SecurityType.WPA2_PSK => WlanApi.DOT11_CIPHER_ALGORITHM.DOT11_CIPHER_ALGO_CCMP,
                SecurityType.WPA3_SAE => WlanApi.DOT11_CIPHER_ALGORITHM.DOT11_CIPHER_ALGO_CCMP,
                _ => WlanApi.DOT11_CIPHER_ALGORITHM.DOT11_CIPHER_ALGO_CCMP
            };

            var securitySettings = new WlanApi.WLAN_HOSTED_NETWORK_SECURITY_SETTINGS
            {
                dot11AuthAlgo = authAlgo,
                dot11CipherAlgo = cipherAlgo
            };

            var settingsPtr = Marshal.AllocHGlobal(Marshal.SizeOf(securitySettings));
            try
            {
                Marshal.StructureToPtr(securitySettings, settingsPtr, false);

                var result = WlanApi.WlanHostedNetworkSetProperty(
                    _wlanHandle,
                    WlanApi.WLAN_HOSTED_NETWORK_OPCODE.wlan_hosted_network_opcode_security_settings,
                    (uint)Marshal.SizeOf(securitySettings),
                    settingsPtr,
                    out WlanApi.WLAN_HOSTED_NETWORK_REASON failReason,
                    IntPtr.Zero);

                return result == WlanApi.ERROR_SUCCESS;
            }
            finally
            {
                Marshal.FreeHGlobal(settingsPtr);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting security settings");
            return false;
        }
    }

    private async Task<bool> SetPasswordAsync(string password)
    {
        try
        {
            var passwordBytes = Encoding.UTF8.GetBytes(password);
            
            var result = WlanApi.WlanHostedNetworkSetSecondaryKey(
                _wlanHandle,
                (uint)passwordBytes.Length,
                passwordBytes,
                true, // isPassPhrase
                true, // persistent
                out WlanApi.WLAN_HOSTED_NETWORK_REASON failReason,
                IntPtr.Zero);

            return result == WlanApi.ERROR_SUCCESS;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting password");
            return false;
        }
    }

    private async Task<bool> EnableHostedNetworkAsync(bool enable)
    {
        try
        {
            var enableValue = enable ? 1 : 0;
            var enablePtr = Marshal.AllocHGlobal(sizeof(int));
            try
            {
                Marshal.WriteInt32(enablePtr, enableValue);

                var result = WlanApi.WlanHostedNetworkSetProperty(
                    _wlanHandle,
                    WlanApi.WLAN_HOSTED_NETWORK_OPCODE.wlan_hosted_network_opcode_enable,
                    sizeof(int),
                    enablePtr,
                    out WlanApi.WLAN_HOSTED_NETWORK_REASON failReason,
                    IntPtr.Zero);

                return result == WlanApi.ERROR_SUCCESS;
            }
            finally
            {
                Marshal.FreeHGlobal(enablePtr);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling/disabling hosted network");
            return false;
        }
    }

    // Additional helper methods will be implemented in the next part...
    
    private void CheckStatusCallback(object? state)
    {
        try
        {
            var status = GetStatusAsync().Result;
            var previousStatus = _currentStatus;
            _currentStatus = status;

            if (status.IsActive != previousStatus.IsActive || 
                status.ConnectedDeviceCount != previousStatus.ConnectedDeviceCount)
            {
                StatusChanged?.Invoke(this, new HotspotStatusChangedEventArgs(status, previousStatus));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in status check callback");
        }
    }

    private void UpdateStatisticsCallback(object? state)
    {
        try
        {
            var statistics = GetNetworkStatisticsAsync().Result;
            StatisticsUpdated?.Invoke(this, new NetworkStatisticsEventArgs(statistics));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in statistics update callback");
        }
    }

    private async Task LogEventAsync(HotspotEvent hotspotEvent)
    {
        _events.Add(hotspotEvent);
        
        // Keep only last 1000 events
        if (_events.Count > 1000)
        {
            _events.RemoveAt(0);
        }

        HotspotEventOccurred?.Invoke(this, new HotspotEventArgs(hotspotEvent));
        _logger.LogInformation("Hotspot event: {EventType} - {Message}", hotspotEvent.EventType, hotspotEvent.Message);
    }

    private int? GetChannelFromFrequency(uint frequency)
    {
        // Convert frequency to channel number (simplified)
        if (frequency >= 2412 && frequency <= 2484)
        {
            return (int)((frequency - 2412) / 5) + 1;
        }
        return null;
    }

    private string? GetNetworkAdapterName(Guid deviceId)
    {
        try
        {
            var adapters = NetworkInterface.GetAllNetworkInterfaces();
            foreach (var adapter in adapters)
            {
                if (adapter.Id.Equals(deviceId.ToString(), StringComparison.OrdinalIgnoreCase))
                {
                    return adapter.Name;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting network adapter name");
        }
        return null;
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _statusTimer?.Dispose();
            _statisticsTimer?.Dispose();

            if (_wlanHandle != IntPtr.Zero)
            {
                WlanApi.WlanCloseHandle(_wlanHandle, IntPtr.Zero);
                _wlanHandle = IntPtr.Zero;
            }

            _disposed = true;
        }
    }

    public async Task<bool> UpdateConfigurationAsync(HotspotConfiguration config)
    {
        try
        {
            var wasActive = _currentStatus.IsActive;

            if (wasActive)
            {
                await StopHotspotAsync();
                await Task.Delay(1000);
            }

            var result = await StartHotspotAsync(config);

            if (result)
            {
                await LogEventAsync(new HotspotEvent
                {
                    EventType = HotspotEventType.ConfigurationChanged,
                    Message = "Hotspot configuration updated"
                });
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating configuration");
            return false;
        }
    }

    public async Task<HotspotConfiguration?> GetConfigurationAsync()
    {
        return _currentConfig;
    }

    public async Task<List<ConnectedDevice>> GetConnectedDevicesAsync()
    {
        return _connectedDevices.Values.ToList();
    }

    public async Task<bool> DisconnectDeviceAsync(string macAddress)
    {
        try
        {
            // Windows doesn't provide direct API to disconnect specific device
            // We'll need to use netsh or WMI for this
            var processInfo = new System.Diagnostics.ProcessStartInfo
            {
                FileName = "netsh",
                Arguments = $"wlan disconnect interface=\"Wi-Fi\"",
                UseShellExecute = false,
                CreateNoWindow = true,
                RedirectStandardOutput = true,
                RedirectStandardError = true
            };

            using var process = System.Diagnostics.Process.Start(processInfo);
            if (process != null)
            {
                await process.WaitForExitAsync();

                if (_connectedDevices.ContainsKey(macAddress))
                {
                    var device = _connectedDevices[macAddress];
                    _connectedDevices.Remove(macAddress);

                    DeviceConnectionChanged?.Invoke(this, new DeviceConnectionEventArgs(device, false));

                    await LogEventAsync(new HotspotEvent
                    {
                        EventType = HotspotEventType.DeviceDisconnected,
                        Message = $"Device disconnected: {macAddress}",
                        DeviceMacAddress = macAddress,
                        DeviceName = device.DeviceName
                    });
                }

                return process.ExitCode == 0;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disconnecting device {MacAddress}", macAddress);
            return false;
        }
    }

    public async Task<bool> BlockDeviceAsync(string macAddress, string? reason = null)
    {
        try
        {
            if (_currentConfig != null)
            {
                if (!_currentConfig.BlockedDevices.Contains(macAddress))
                {
                    _currentConfig.BlockedDevices.Add(macAddress);
                }

                // Remove from allowed devices if present
                _currentConfig.AllowedDevices.Remove(macAddress);

                // Disconnect if currently connected
                if (_connectedDevices.ContainsKey(macAddress))
                {
                    await DisconnectDeviceAsync(macAddress);
                }

                await LogEventAsync(new HotspotEvent
                {
                    EventType = HotspotEventType.DeviceBlocked,
                    Message = $"Device blocked: {macAddress}. Reason: {reason ?? "Manual block"}",
                    DeviceMacAddress = macAddress
                });

                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error blocking device {MacAddress}", macAddress);
            return false;
        }
    }

    public async Task<bool> UnblockDeviceAsync(string macAddress)
    {
        try
        {
            if (_currentConfig != null)
            {
                _currentConfig.BlockedDevices.Remove(macAddress);

                await LogEventAsync(new HotspotEvent
                {
                    EventType = HotspotEventType.Information,
                    Message = $"Device unblocked: {macAddress}",
                    DeviceMacAddress = macAddress
                });

                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unblocking device {MacAddress}", macAddress);
            return false;
        }
    }

    public async Task<NetworkStatistics> GetNetworkStatisticsAsync()
    {
        try
        {
            var statistics = new NetworkStatistics
            {
                LastUpdated = DateTime.UtcNow
            };

            // Get network interface statistics
            var adapters = NetworkInterface.GetAllNetworkInterfaces();
            var hostedNetworkAdapter = adapters.FirstOrDefault(a =>
                a.Name.Contains("Microsoft Hosted Network Virtual Adapter") ||
                a.Description.Contains("Microsoft Hosted Network Virtual Adapter"));

            if (hostedNetworkAdapter != null)
            {
                var stats = hostedNetworkAdapter.GetIPv4Statistics();
                statistics.TotalBytesReceived = stats.BytesReceived;
                statistics.TotalBytesSent = stats.BytesSent;

                // Calculate current speeds (simplified - would need historical data for accurate calculation)
                statistics.CurrentDownloadSpeedMbps = 0; // Would need to track over time
                statistics.CurrentUploadSpeedMbps = 0;   // Would need to track over time
            }

            if (_currentStatus.StartedAt.HasValue)
            {
                statistics.Uptime = DateTime.UtcNow - _currentStatus.StartedAt.Value;
            }

            return statistics;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting network statistics");
            return new NetworkStatistics { LastUpdated = DateTime.UtcNow };
        }
    }

    public async Task<bool> SetBandwidthLimitsAsync(BandwidthLimits limits)
    {
        try
        {
            // Windows doesn't provide direct API for bandwidth limiting
            // This would typically require QoS policies or third-party tools
            _logger.LogWarning("Bandwidth limiting not directly supported by Windows API");

            if (_currentConfig != null)
            {
                _currentConfig.BandwidthLimits = limits;
                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting bandwidth limits");
            return false;
        }
    }

    public async Task<bool> SetDeviceBandwidthLimitsAsync(string macAddress, BandwidthLimits limits)
    {
        try
        {
            // Per-device bandwidth limiting would require advanced QoS configuration
            _logger.LogWarning("Per-device bandwidth limiting not directly supported");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting device bandwidth limits for {MacAddress}", macAddress);
            return false;
        }
    }

    public async Task<List<HotspotEvent>> GetEventsAsync(DateTime? startTime = null, DateTime? endTime = null, HotspotEventType? eventType = null)
    {
        try
        {
            var filteredEvents = _events.AsEnumerable();

            if (startTime.HasValue)
                filteredEvents = filteredEvents.Where(e => e.Timestamp >= startTime.Value);

            if (endTime.HasValue)
                filteredEvents = filteredEvents.Where(e => e.Timestamp <= endTime.Value);

            if (eventType.HasValue)
                filteredEvents = filteredEvents.Where(e => e.EventType == eventType.Value);

            return filteredEvents.OrderByDescending(e => e.Timestamp).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting events");
            return new List<HotspotEvent>();
        }
    }

    public async Task<bool> ClearEventLogAsync()
    {
        try
        {
            _events.Clear();
            await LogEventAsync(new HotspotEvent
            {
                EventType = HotspotEventType.Information,
                Message = "Event log cleared"
            });
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing event log");
            return false;
        }
    }

    public async Task<bool> SaveProfileAsync(HotspotConfiguration config, string profileName)
    {
        try
        {
            var profilesDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "HotspotManager", "Profiles");
            Directory.CreateDirectory(profilesDir);

            var profilePath = Path.Combine(profilesDir, $"{profileName}.json");
            var json = Newtonsoft.Json.JsonConvert.SerializeObject(config, Newtonsoft.Json.Formatting.Indented);

            await File.WriteAllTextAsync(profilePath, json);

            await LogEventAsync(new HotspotEvent
            {
                EventType = HotspotEventType.Information,
                Message = $"Profile saved: {profileName}"
            });

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving profile {ProfileName}", profileName);
            return false;
        }
    }

    public async Task<HotspotConfiguration?> LoadProfileAsync(string profileName)
    {
        try
        {
            var profilesDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "HotspotManager", "Profiles");
            var profilePath = Path.Combine(profilesDir, $"{profileName}.json");

            if (!File.Exists(profilePath))
                return null;

            var json = await File.ReadAllTextAsync(profilePath);
            var config = Newtonsoft.Json.JsonConvert.DeserializeObject<HotspotConfiguration>(json);

            return config;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading profile {ProfileName}", profileName);
            return null;
        }
    }

    public async Task<List<string>> GetProfileNamesAsync()
    {
        try
        {
            var profilesDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "HotspotManager", "Profiles");

            if (!Directory.Exists(profilesDir))
                return new List<string>();

            var files = Directory.GetFiles(profilesDir, "*.json");
            return files.Select(f => Path.GetFileNameWithoutExtension(f)).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting profile names");
            return new List<string>();
        }
    }

    public async Task<bool> DeleteProfileAsync(string profileName)
    {
        try
        {
            var profilesDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "HotspotManager", "Profiles");
            var profilePath = Path.Combine(profilesDir, $"{profileName}.json");

            if (File.Exists(profilePath))
            {
                File.Delete(profilePath);

                await LogEventAsync(new HotspotEvent
                {
                    EventType = HotspotEventType.Information,
                    Message = $"Profile deleted: {profileName}"
                });

                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting profile {ProfileName}", profileName);
            return false;
        }
    }

    public async Task<bool> ScheduleStartAsync(DateTime startTime, HotspotConfiguration? config = null)
    {
        try
        {
            // Simple scheduling implementation - in production, use Windows Task Scheduler or Quartz.NET
            var delay = startTime - DateTime.Now;
            if (delay.TotalMilliseconds > 0)
            {
                _ = Task.Delay(delay).ContinueWith(async _ =>
                {
                    var configToUse = config ?? _currentConfig ?? new HotspotConfiguration();
                    await StartHotspotAsync(configToUse);
                });

                await LogEventAsync(new HotspotEvent
                {
                    EventType = HotspotEventType.Information,
                    Message = $"Hotspot scheduled to start at {startTime}"
                });

                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling start");
            return false;
        }
    }

    public async Task<bool> ScheduleStopAsync(DateTime stopTime)
    {
        try
        {
            var delay = stopTime - DateTime.Now;
            if (delay.TotalMilliseconds > 0)
            {
                _ = Task.Delay(delay).ContinueWith(async _ =>
                {
                    await StopHotspotAsync();
                });

                await LogEventAsync(new HotspotEvent
                {
                    EventType = HotspotEventType.Information,
                    Message = $"Hotspot scheduled to stop at {stopTime}"
                });

                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error scheduling stop");
            return false;
        }
    }

    public async Task<bool> CancelScheduledOperationsAsync()
    {
        try
        {
            // In a full implementation, we'd track and cancel scheduled tasks
            await LogEventAsync(new HotspotEvent
            {
                EventType = HotspotEventType.Information,
                Message = "Scheduled operations cancelled"
            });

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling scheduled operations");
            return false;
        }
    }

    public async Task<List<NetworkAdapter>> GetAvailableAdaptersAsync()
    {
        try
        {
            var adapters = new List<NetworkAdapter>();
            var networkInterfaces = NetworkInterface.GetAllNetworkInterfaces();

            foreach (var ni in networkInterfaces)
            {
                var adapter = new NetworkAdapter
                {
                    Id = ni.Id,
                    Name = ni.Name,
                    Description = ni.Description,
                    IsWireless = ni.NetworkInterfaceType == NetworkInterfaceType.Wireless80211,
                    IsConnected = ni.OperationalStatus == OperationalStatus.Up,
                    Type = GetAdapterType(ni.NetworkInterfaceType)
                };

                // Get MAC address
                var physicalAddress = ni.GetPhysicalAddress();
                if (physicalAddress != null)
                {
                    adapter.MacAddress = string.Join(":", physicalAddress.GetAddressBytes().Select(b => b.ToString("X2")));
                }

                // Get IP address
                var ipProps = ni.GetIPProperties();
                var ipAddress = ipProps.UnicastAddresses.FirstOrDefault(ip => ip.Address.AddressFamily == System.Net.Sockets.AddressFamily.InterNetwork);
                if (ipAddress != null)
                {
                    adapter.IPAddress = ipAddress.Address.ToString();
                }

                // Check if supports hosted network (simplified check)
                adapter.SupportsHostedNetwork = adapter.IsWireless && adapter.Name.Contains("Wi-Fi");

                adapters.Add(adapter);
            }

            return adapters;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting available adapters");
            return [];
        }
    }

    public async Task<bool> SetNetworkAdapterAsync(string adapterId)
    {
        try
        {
            // In a full implementation, we'd configure the specific adapter for hosted network
            _logger.LogInformation("Setting network adapter: {AdapterId}", adapterId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting network adapter {AdapterId}", adapterId);
            return false;
        }
    }

    public async Task<bool> TestConnectivityAsync()
    {
        try
        {
            using var ping = new System.Net.NetworkInformation.Ping();
            var reply = await ping.SendPingAsync("*******", 5000);
            return reply.Status == System.Net.NetworkInformation.IPStatus.Success;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error testing connectivity");
            return false;
        }
    }

    public async Task<bool> ExportDataAsync(string filePath, bool includeEvents = true, bool includeStatistics = true)
    {
        try
        {
            var exportData = new
            {
                Configuration = _currentConfig,
                Status = _currentStatus,
                Events = includeEvents ? _events : null,
                Statistics = includeStatistics ? await GetNetworkStatisticsAsync() : null,
                ConnectedDevices = _connectedDevices.Values.ToList(),
                ExportedAt = DateTime.UtcNow
            };

            var json = Newtonsoft.Json.JsonConvert.SerializeObject(exportData, Newtonsoft.Json.Formatting.Indented);
            await File.WriteAllTextAsync(filePath, json);

            await LogEventAsync(new HotspotEvent
            {
                EventType = HotspotEventType.Information,
                Message = $"Data exported to {filePath}"
            });

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting data to {FilePath}", filePath);
            return false;
        }
    }

    public async Task<bool> ImportConfigurationAsync(string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
                return false;

            var json = await File.ReadAllTextAsync(filePath);
            var importData = Newtonsoft.Json.JsonConvert.DeserializeAnonymousType(json, new { Configuration = (HotspotConfiguration?)null });

            if (importData?.Configuration != null)
            {
                await UpdateConfigurationAsync(importData.Configuration);

                await LogEventAsync(new HotspotEvent
                {
                    EventType = HotspotEventType.Information,
                    Message = $"Configuration imported from {filePath}"
                });

                return true;
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing configuration from {FilePath}", filePath);
            return false;
        }
    }

    private async Task UpdateConnectedDevicesAsync(WlanApi.WLAN_HOSTED_NETWORK_STATUS status)
    {
        try
        {
            var currentDevices = new Dictionary<string, ConnectedDevice>();

            if (status.dwNumberOfPeers > 0 && status.PeerList != IntPtr.Zero)
            {
                for (int i = 0; i < status.dwNumberOfPeers; i++)
                {
                    var peerPtr = IntPtr.Add(status.PeerList, i * Marshal.SizeOf<WlanApi.WLAN_HOSTED_NETWORK_PEER_STATE>());
                    var peer = Marshal.PtrToStructure<WlanApi.WLAN_HOSTED_NETWORK_PEER_STATE>(peerPtr);

                    var macAddress = string.Join(":", peer.PeerMacAddress.Select(b => b.ToString("X2")));

                    var device = new ConnectedDevice
                    {
                        MacAddress = macAddress,
                        ConnectedAt = DateTime.UtcNow, // Should track actual connection time
                        LastSeen = DateTime.UtcNow,
                        IPAddress = GetDeviceIPAddress(macAddress) ?? "Unknown"
                    };

                    currentDevices[macAddress] = device;

                    // Check if this is a new connection
                    if (!_connectedDevices.ContainsKey(macAddress))
                    {
                        _connectedDevices[macAddress] = device;
                        DeviceConnectionChanged?.Invoke(this, new DeviceConnectionEventArgs(device, true));

                        await LogEventAsync(new HotspotEvent
                        {
                            EventType = HotspotEventType.DeviceConnected,
                            Message = $"Device connected: {macAddress}",
                            DeviceMacAddress = macAddress
                        });
                    }
                    else
                    {
                        // Update existing device
                        _connectedDevices[macAddress].LastSeen = DateTime.UtcNow;
                    }
                }
            }

            // Check for disconnected devices
            var disconnectedDevices = _connectedDevices.Keys.Except(currentDevices.Keys).ToList();
            foreach (var macAddress in disconnectedDevices)
            {
                var device = _connectedDevices[macAddress];
                _connectedDevices.Remove(macAddress);

                DeviceConnectionChanged?.Invoke(this, new DeviceConnectionEventArgs(device, false));

                await LogEventAsync(new HotspotEvent
                {
                    EventType = HotspotEventType.DeviceDisconnected,
                    Message = $"Device disconnected: {macAddress}",
                    DeviceMacAddress = macAddress,
                    DeviceName = device.DeviceName
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating connected devices");
        }
    }

    private string? GetDeviceIPAddress(string macAddress)
    {
        try
        {
            // Use ARP table to find IP address for MAC address
            var processInfo = new System.Diagnostics.ProcessStartInfo
            {
                FileName = "arp",
                Arguments = "-a",
                UseShellExecute = false,
                CreateNoWindow = true,
                RedirectStandardOutput = true
            };

            using var process = System.Diagnostics.Process.Start(processInfo);
            if (process != null)
            {
                var output = process.StandardOutput.ReadToEnd();
                process.WaitForExit();

                var lines = output.Split('\n');
                foreach (var line in lines)
                {
                    if (line.Contains(macAddress.Replace(":", "-"), StringComparison.OrdinalIgnoreCase))
                    {
                        var parts = line.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length > 0 && IPAddress.TryParse(parts[0], out _))
                        {
                            return parts[0];
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting device IP address for MAC {MacAddress}", macAddress);
        }

        return null;
    }

    private static AdapterType GetAdapterType(NetworkInterfaceType interfaceType)
    {
        return interfaceType switch
        {
            NetworkInterfaceType.Ethernet => AdapterType.Ethernet,
            NetworkInterfaceType.Wireless80211 => AdapterType.Wireless,
            NetworkInterfaceType.Loopback => AdapterType.Virtual,
            NetworkInterfaceType.Tunnel => AdapterType.Virtual,
            _ => AdapterType.Unknown
        };
    }
}
