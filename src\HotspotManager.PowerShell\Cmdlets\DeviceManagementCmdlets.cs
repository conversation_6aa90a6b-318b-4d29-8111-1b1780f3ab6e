using System.Management.Automation;
using Microsoft.Extensions.Logging;
using HotspotManager.Core.Models;
using HotspotManager.Core.Services;

namespace HotspotManager.PowerShell.Cmdlets;

/// <summary>
/// PowerShell cmdlet to get connected devices
/// </summary>
[Cmdlet(VerbsCommon.Get, "HotspotDevices")]
[OutputType(typeof(ConnectedDevice[]))]
public class GetHotspotDevicesCmdlet : PSCmdlet
{
    private IHotspotService? _hotspotService;

    protected override void BeginProcessing()
    {
        try
        {
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<WindowsHotspotService>();
            
            _hotspotService = new WindowsHotspotService(logger);
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "InitializationError", ErrorCategory.InvalidOperation, null));
            return;
        }
    }

    protected override void ProcessRecord()
    {
        try
        {
            if (_hotspotService == null)
            {
                WriteError(new ErrorRecord(new InvalidOperationException("Hotspot service not initialized"), 
                    "ServiceNotInitialized", ErrorCategory.InvalidOperation, null));
                return;
            }

            var devices = _hotspotService.GetConnectedDevicesAsync().Result;
            WriteObject(devices.ToArray());
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "ProcessingError", ErrorCategory.InvalidOperation, null));
        }
    }

    protected override void EndProcessing()
    {
        try
        {
            if (_hotspotService is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
        catch (Exception ex)
        {
            WriteWarning($"Error disposing hotspot service: {ex.Message}");
        }
    }
}

/// <summary>
/// PowerShell cmdlet to disconnect a device
/// </summary>
[Cmdlet(VerbsCommon.Remove, "HotspotDevice")]
[OutputType(typeof(bool))]
public class RemoveHotspotDeviceCmdlet : PSCmdlet
{
    [Parameter(Mandatory = true, Position = 0, HelpMessage = "MAC address of the device to disconnect")]
    [ValidateNotNullOrEmpty]
    public string MacAddress { get; set; } = string.Empty;

    [Parameter(HelpMessage = "Also block the device from reconnecting")]
    public SwitchParameter Block { get; set; }

    [Parameter(HelpMessage = "Reason for blocking (if Block is specified)")]
    public string? BlockReason { get; set; }

    private IHotspotService? _hotspotService;

    protected override void BeginProcessing()
    {
        try
        {
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<WindowsHotspotService>();
            
            _hotspotService = new WindowsHotspotService(logger);
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "InitializationError", ErrorCategory.InvalidOperation, null));
            return;
        }
    }

    protected override void ProcessRecord()
    {
        try
        {
            if (_hotspotService == null)
            {
                WriteError(new ErrorRecord(new InvalidOperationException("Hotspot service not initialized"), 
                    "ServiceNotInitialized", ErrorCategory.InvalidOperation, null));
                return;
            }

            WriteVerbose($"Disconnecting device: {MacAddress}");
            
            // Disconnect the device
            var disconnectResult = _hotspotService.DisconnectDeviceAsync(MacAddress).Result;
            
            if (!disconnectResult)
            {
                WriteError(new ErrorRecord(new InvalidOperationException($"Failed to disconnect device {MacAddress}"), 
                    "DisconnectFailed", ErrorCategory.InvalidOperation, MacAddress));
                WriteObject(false);
                return;
            }

            // Block the device if requested
            if (Block.IsPresent)
            {
                WriteVerbose($"Blocking device: {MacAddress}");
                var blockResult = _hotspotService.BlockDeviceAsync(MacAddress, BlockReason).Result;
                
                if (!blockResult)
                {
                    WriteWarning($"Device disconnected but failed to block: {MacAddress}");
                }
                else
                {
                    WriteInformation($"Device {MacAddress} disconnected and blocked", new string[] { "Success" });
                }
            }
            else
            {
                WriteInformation($"Device {MacAddress} disconnected", new string[] { "Success" });
            }

            WriteObject(true);
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "ProcessingError", ErrorCategory.InvalidOperation, null));
            WriteObject(false);
        }
    }

    protected override void EndProcessing()
    {
        try
        {
            if (_hotspotService is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
        catch (Exception ex)
        {
            WriteWarning($"Error disposing hotspot service: {ex.Message}");
        }
    }
}

/// <summary>
/// PowerShell cmdlet to block a device
/// </summary>
[Cmdlet(VerbsSecurity.Block, "HotspotDevice")]
[OutputType(typeof(bool))]
public class BlockHotspotDeviceCmdlet : PSCmdlet
{
    [Parameter(Mandatory = true, Position = 0, HelpMessage = "MAC address of the device to block")]
    [ValidateNotNullOrEmpty]
    public string MacAddress { get; set; } = string.Empty;

    [Parameter(HelpMessage = "Reason for blocking")]
    public string? Reason { get; set; }

    private IHotspotService? _hotspotService;

    protected override void BeginProcessing()
    {
        try
        {
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<WindowsHotspotService>();
            
            _hotspotService = new WindowsHotspotService(logger);
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "InitializationError", ErrorCategory.InvalidOperation, null));
            return;
        }
    }

    protected override void ProcessRecord()
    {
        try
        {
            if (_hotspotService == null)
            {
                WriteError(new ErrorRecord(new InvalidOperationException("Hotspot service not initialized"), 
                    "ServiceNotInitialized", ErrorCategory.InvalidOperation, null));
                return;
            }

            WriteVerbose($"Blocking device: {MacAddress}");
            
            var result = _hotspotService.BlockDeviceAsync(MacAddress, Reason).Result;
            
            if (result)
            {
                WriteObject(true);
                WriteInformation($"Device {MacAddress} blocked successfully", new string[] { "Success" });
            }
            else
            {
                WriteError(new ErrorRecord(new InvalidOperationException($"Failed to block device {MacAddress}"), 
                    "BlockFailed", ErrorCategory.InvalidOperation, MacAddress));
                WriteObject(false);
            }
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "ProcessingError", ErrorCategory.InvalidOperation, null));
            WriteObject(false);
        }
    }

    protected override void EndProcessing()
    {
        try
        {
            if (_hotspotService is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
        catch (Exception ex)
        {
            WriteWarning($"Error disposing hotspot service: {ex.Message}");
        }
    }
}

/// <summary>
/// PowerShell cmdlet to unblock a device
/// </summary>
[Cmdlet(VerbsSecurity.Unblock, "HotspotDevice")]
[OutputType(typeof(bool))]
public class UnblockHotspotDeviceCmdlet : PSCmdlet
{
    [Parameter(Mandatory = true, Position = 0, HelpMessage = "MAC address of the device to unblock")]
    [ValidateNotNullOrEmpty]
    public string MacAddress { get; set; } = string.Empty;

    private IHotspotService? _hotspotService;

    protected override void BeginProcessing()
    {
        try
        {
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<WindowsHotspotService>();
            
            _hotspotService = new WindowsHotspotService(logger);
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "InitializationError", ErrorCategory.InvalidOperation, null));
            return;
        }
    }

    protected override void ProcessRecord()
    {
        try
        {
            if (_hotspotService == null)
            {
                WriteError(new ErrorRecord(new InvalidOperationException("Hotspot service not initialized"), 
                    "ServiceNotInitialized", ErrorCategory.InvalidOperation, null));
                return;
            }

            WriteVerbose($"Unblocking device: {MacAddress}");
            
            var result = _hotspotService.UnblockDeviceAsync(MacAddress).Result;
            
            if (result)
            {
                WriteObject(true);
                WriteInformation($"Device {MacAddress} unblocked successfully", new string[] { "Success" });
            }
            else
            {
                WriteError(new ErrorRecord(new InvalidOperationException($"Failed to unblock device {MacAddress}"), 
                    "UnblockFailed", ErrorCategory.InvalidOperation, MacAddress));
                WriteObject(false);
            }
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "ProcessingError", ErrorCategory.InvalidOperation, null));
            WriteObject(false);
        }
    }

    protected override void EndProcessing()
    {
        try
        {
            if (_hotspotService is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
        catch (Exception ex)
        {
            WriteWarning($"Error disposing hotspot service: {ex.Message}");
        }
    }
}
