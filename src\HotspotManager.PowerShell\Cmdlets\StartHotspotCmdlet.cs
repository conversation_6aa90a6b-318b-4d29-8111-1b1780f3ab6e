using System.Management.Automation;
using Microsoft.Extensions.Logging;
using HotspotManager.Core.Models;
using HotspotManager.Core.Services;

namespace HotspotManager.PowerShell.Cmdlets;

/// <summary>
/// PowerShell cmdlet to start the mobile hotspot
/// </summary>
[Cmdlet(VerbsLifecycle.Start, "Hotspot")]
[OutputType(typeof(bool))]
public class StartHotspotCmdlet : PSCmdlet
{
    [Parameter(Mandatory = true, Position = 0, HelpMessage = "SSID for the hotspot")]
    [ValidateNotNullOrEmpty]
    public string SSID { get; set; } = string.Empty;

    [Parameter(Mandatory = true, Position = 1, HelpMessage = "Password for the hotspot")]
    [ValidateLength(8, 63)]
    public string Password { get; set; } = string.Empty;

    [Parameter(HelpMessage = "Maximum number of connections (1-8)")]
    [ValidateRange(1, 8)]
    public int MaxConnections { get; set; } = 8;

    [Parameter(HelpMessage = "Security type (Open, WEP, WPA_PSK, WPA2_PSK, WPA3_SAE)")]
    public SecurityType Security { get; set; } = SecurityType.WPA2_PSK;

    [Parameter(HelpMessage = "Channel number")]
    [ValidateRange(1, 11)]
    public int Channel { get; set; } = 6;

    [Parameter(HelpMessage = "Hide the network SSID")]
    public SwitchParameter Hidden { get; set; }

    [Parameter(HelpMessage = "Enable auto-start")]
    public SwitchParameter AutoStart { get; set; }

    [Parameter(HelpMessage = "Auto-stop after specified minutes")]
    public int? AutoStopAfterMinutes { get; set; }

    [Parameter(HelpMessage = "Download speed limit in Mbps")]
    public double? DownloadLimitMbps { get; set; }

    [Parameter(HelpMessage = "Upload speed limit in Mbps")]
    public double? UploadLimitMbps { get; set; }

    [Parameter(HelpMessage = "Enable guest network isolation")]
    public SwitchParameter GuestIsolation { get; set; }

    private IHotspotService? _hotspotService;

    protected override void BeginProcessing()
    {
        try
        {
            // Initialize logging
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<WindowsHotspotService>();
            
            _hotspotService = new WindowsHotspotService(logger);
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "InitializationError", ErrorCategory.InvalidOperation, null));
            return;
        }
    }

    protected override void ProcessRecord()
    {
        try
        {
            if (_hotspotService == null)
            {
                WriteError(new ErrorRecord(new InvalidOperationException("Hotspot service not initialized"), 
                    "ServiceNotInitialized", ErrorCategory.InvalidOperation, null));
                return;
            }

            // Check if hotspot is available
            var isAvailable = _hotspotService.IsHotspotAvailableAsync().Result;
            if (!isAvailable)
            {
                WriteError(new ErrorRecord(new NotSupportedException("Mobile hotspot is not available on this system"), 
                    "HotspotNotAvailable", ErrorCategory.NotInstalled, null));
                return;
            }

            // Create configuration
            var config = new HotspotConfiguration
            {
                SSID = SSID,
                Password = Password,
                MaxConnections = MaxConnections,
                Security = Security,
                Channel = Channel,
                IsHidden = Hidden.IsPresent,
                AutoStart = AutoStart.IsPresent,
                GuestNetworkIsolation = GuestIsolation.IsPresent
            };

            // Set auto-stop time if specified
            if (AutoStopAfterMinutes.HasValue)
            {
                config.AutoStopAfter = TimeSpan.FromMinutes(AutoStopAfterMinutes.Value);
            }

            // Set bandwidth limits if specified
            if (DownloadLimitMbps.HasValue || UploadLimitMbps.HasValue)
            {
                config.BandwidthLimits = new BandwidthLimits
                {
                    DownloadLimitMbps = DownloadLimitMbps ?? 0,
                    UploadLimitMbps = UploadLimitMbps ?? 0
                };
            }

            WriteVerbose($"Starting hotspot with SSID: {SSID}");
            
            // Start the hotspot
            var result = _hotspotService.StartHotspotAsync(config).Result;
            
            if (result)
            {
                WriteObject(true);
                WriteInformation($"Hotspot '{SSID}' started successfully", new string[] { "Success" });
            }
            else
            {
                WriteError(new ErrorRecord(new InvalidOperationException("Failed to start hotspot"), 
                    "StartFailed", ErrorCategory.InvalidOperation, config));
                WriteObject(false);
            }
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "ProcessingError", ErrorCategory.InvalidOperation, null));
            WriteObject(false);
        }
    }

    protected override void EndProcessing()
    {
        try
        {
            if (_hotspotService is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
        catch (Exception ex)
        {
            WriteWarning($"Error disposing hotspot service: {ex.Message}");
        }
    }
}

/// <summary>
/// PowerShell cmdlet to stop the mobile hotspot
/// </summary>
[Cmdlet(VerbsLifecycle.Stop, "Hotspot")]
[OutputType(typeof(bool))]
public class StopHotspotCmdlet : PSCmdlet
{
    [Parameter(HelpMessage = "Force stop even if devices are connected")]
    public SwitchParameter Force { get; set; }

    private IHotspotService? _hotspotService;

    protected override void BeginProcessing()
    {
        try
        {
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<WindowsHotspotService>();
            
            _hotspotService = new WindowsHotspotService(logger);
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "InitializationError", ErrorCategory.InvalidOperation, null));
            return;
        }
    }

    protected override void ProcessRecord()
    {
        try
        {
            if (_hotspotService == null)
            {
                WriteError(new ErrorRecord(new InvalidOperationException("Hotspot service not initialized"), 
                    "ServiceNotInitialized", ErrorCategory.InvalidOperation, null));
                return;
            }

            // Check current status
            var status = _hotspotService.GetStatusAsync().Result;
            if (!status.IsActive)
            {
                WriteInformation("Hotspot is not currently active", new string[] { "Info" });
                WriteObject(true);
                return;
            }

            // Check for connected devices if not forcing
            if (!Force.IsPresent && status.ConnectedDeviceCount > 0)
            {
                var shouldContinue = ShouldContinue(
                    $"There are {status.ConnectedDeviceCount} devices connected to the hotspot. Do you want to continue?",
                    "Devices Connected");
                
                if (!shouldContinue)
                {
                    WriteObject(false);
                    return;
                }
            }

            WriteVerbose("Stopping hotspot");
            
            // Stop the hotspot
            var result = _hotspotService.StopHotspotAsync().Result;
            
            if (result)
            {
                WriteObject(true);
                WriteInformation("Hotspot stopped successfully", new string[] { "Success" });
            }
            else
            {
                WriteError(new ErrorRecord(new InvalidOperationException("Failed to stop hotspot"), 
                    "StopFailed", ErrorCategory.InvalidOperation, null));
                WriteObject(false);
            }
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "ProcessingError", ErrorCategory.InvalidOperation, null));
            WriteObject(false);
        }
    }

    protected override void EndProcessing()
    {
        try
        {
            if (_hotspotService is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
        catch (Exception ex)
        {
            WriteWarning($"Error disposing hotspot service: {ex.Message}");
        }
    }
}

/// <summary>
/// PowerShell cmdlet to get hotspot status
/// </summary>
[Cmdlet(VerbsCommon.Get, "HotspotStatus")]
[OutputType(typeof(HotspotStatus))]
public class GetHotspotStatusCmdlet : PSCmdlet
{
    private IHotspotService? _hotspotService;

    protected override void BeginProcessing()
    {
        try
        {
            using var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var logger = loggerFactory.CreateLogger<WindowsHotspotService>();
            
            _hotspotService = new WindowsHotspotService(logger);
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "InitializationError", ErrorCategory.InvalidOperation, null));
            return;
        }
    }

    protected override void ProcessRecord()
    {
        try
        {
            if (_hotspotService == null)
            {
                WriteError(new ErrorRecord(new InvalidOperationException("Hotspot service not initialized"), 
                    "ServiceNotInitialized", ErrorCategory.InvalidOperation, null));
                return;
            }

            var status = _hotspotService.GetStatusAsync().Result;
            WriteObject(status);
        }
        catch (Exception ex)
        {
            WriteError(new ErrorRecord(ex, "ProcessingError", ErrorCategory.InvalidOperation, null));
        }
    }

    protected override void EndProcessing()
    {
        try
        {
            if (_hotspotService is IDisposable disposable)
            {
                disposable.Dispose();
            }
        }
        catch (Exception ex)
        {
            WriteWarning($"Error disposing hotspot service: {ex.Message}");
        }
    }
}
