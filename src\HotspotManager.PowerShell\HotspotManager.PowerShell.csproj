<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
    <AssemblyTitle>HotspotManager PowerShell Module</AssemblyTitle>
    <AssemblyDescription>PowerShell module for Windows Mobile Hotspot management</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Company>HotspotManager</Company>
    <Product>Windows Mobile Hotspot Manager</Product>
    <Copyright>Copyright © 2024</Copyright>
    <Platforms>AnyCPU;x64;x86</Platforms>
    <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.PowerShell.SDK" Version="7.4.0" />
    <PackageReference Include="PowerShellStandard.Library" Version="5.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\HotspotManager.Core\HotspotManager.Core.csproj" />
  </ItemGroup>

</Project>
