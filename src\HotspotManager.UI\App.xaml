<Application x:Class="HotspotManager.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:HotspotManager.UI.Converters"
             StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Orange" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- Custom Styles -->
                <ResourceDictionary>
                    <!-- Value Converters -->
                    <local:BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
                    <local:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter"/>
                    <local:StringToVisibilityConverter x:Key="StringToVisibilityConverter"/>
                    <local:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
                    <local:BoolToStringConverter x:Key="BoolToStringConverter"/>
                    <local:BytesToHumanReadableConverter x:Key="BytesToHumanReadableConverter"/>
                    <local:EventTypeToColorConverter x:Key="EventTypeToColorConverter"/>
                    <!-- Card Style -->
                    <Style x:Key="InfoCard" TargetType="materialDesign:Card">
                        <Setter Property="Margin" Value="8"/>
                        <Setter Property="Padding" Value="16"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
                    </Style>
                    
                    <!-- Status Indicator -->
                    <Style x:Key="StatusIndicator" TargetType="Ellipse">
                        <Setter Property="Width" Value="12"/>
                        <Setter Property="Height" Value="12"/>
                        <Setter Property="Margin" Value="0,0,8,0"/>
                        <Setter Property="VerticalAlignment" Value="Center"/>
                    </Style>
                    
                    <!-- Active Status -->
                    <Style x:Key="ActiveStatus" TargetType="Ellipse" BasedOn="{StaticResource StatusIndicator}">
                        <Setter Property="Fill" Value="#4CAF50"/>
                    </Style>
                    
                    <!-- Inactive Status -->
                    <Style x:Key="InactiveStatus" TargetType="Ellipse" BasedOn="{StaticResource StatusIndicator}">
                        <Setter Property="Fill" Value="#F44336"/>
                    </Style>
                    
                    <!-- Warning Status -->
                    <Style x:Key="WarningStatus" TargetType="Ellipse" BasedOn="{StaticResource StatusIndicator}">
                        <Setter Property="Fill" Value="#FF9800"/>
                    </Style>
                    
                    <!-- Primary Button -->
                    <Style x:Key="PrimaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="Padding" Value="16,8"/>
                        <Setter Property="Background" Value="{DynamicResource PrimaryHueMidBrush}"/>
                        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidForegroundBrush}"/>
                    </Style>
                    
                    <!-- Secondary Button -->
                    <Style x:Key="SecondaryButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="Padding" Value="16,8"/>
                    </Style>
                    
                    <!-- Danger Button -->
                    <Style x:Key="DangerButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
                        <Setter Property="Margin" Value="4"/>
                        <Setter Property="Padding" Value="16,8"/>
                        <Setter Property="Background" Value="#F44336"/>
                        <Setter Property="Foreground" Value="White"/>
                    </Style>
                    
                    <!-- Data Grid Style -->
                    <Style x:Key="ModernDataGrid" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
                        <Setter Property="AutoGenerateColumns" Value="False"/>
                        <Setter Property="CanUserAddRows" Value="False"/>
                        <Setter Property="CanUserDeleteRows" Value="False"/>
                        <Setter Property="IsReadOnly" Value="True"/>
                        <Setter Property="SelectionMode" Value="Single"/>
                        <Setter Property="GridLinesVisibility" Value="Horizontal"/>
                        <Setter Property="HeadersVisibility" Value="Column"/>
                        <Setter Property="Margin" Value="8"/>
                    </Style>
                    
                    <!-- Text Input Style -->
                    <Style x:Key="ModernTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
                        <Setter Property="Margin" Value="0,8"/>
                        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
                    </Style>
                    
                    <!-- Password Input Style -->
                    <Style x:Key="ModernPasswordBox" TargetType="PasswordBox" BasedOn="{StaticResource MaterialDesignOutlinedPasswordBox}">
                        <Setter Property="Margin" Value="0,8"/>
                        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
                    </Style>
                    
                    <!-- ComboBox Style -->
                    <Style x:Key="ModernComboBox" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
                        <Setter Property="Margin" Value="0,8"/>
                        <Setter Property="materialDesign:HintAssist.IsFloating" Value="True"/>
                    </Style>
                    
                    <!-- Numeric Input Style -->
                    <Style x:Key="NumericTextBox" TargetType="TextBox" BasedOn="{StaticResource ModernTextBox}">
                        <Setter Property="materialDesign:TextFieldAssist.SuffixText" Value=""/>
                    </Style>
                    
                    <!-- Section Header -->
                    <Style x:Key="SectionHeader" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="18"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                        <Setter Property="Margin" Value="0,16,0,8"/>
                        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
                    </Style>
                    
                    <!-- Metric Card -->
                    <Style x:Key="MetricCard" TargetType="materialDesign:Card" BasedOn="{StaticResource InfoCard}">
                        <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
                        <Setter Property="MinHeight" Value="80"/>
                    </Style>
                    
                    <!-- Metric Value -->
                    <Style x:Key="MetricValue" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="24"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Setter Property="HorizontalAlignment" Value="Center"/>
                        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
                    </Style>
                    
                    <!-- Metric Label -->
                    <Style x:Key="MetricLabel" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="12"/>
                        <Setter Property="HorizontalAlignment" Value="Center"/>
                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                        <Setter Property="Margin" Value="0,4,0,0"/>
                    </Style>
                    
                    <!-- Tab Style -->
                    <Style x:Key="ModernTabControl" TargetType="TabControl" BasedOn="{StaticResource MaterialDesignTabControl}">
                        <Setter Property="materialDesign:ColorZoneAssist.Mode" Value="PrimaryMid"/>
                    </Style>
                    
                    <!-- Progress Bar Style -->
                    <Style x:Key="ModernProgressBar" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignLinearProgressBar}">
                        <Setter Property="Height" Value="4"/>
                        <Setter Property="Margin" Value="0,8"/>
                    </Style>
                    
                    <!-- Toggle Button Style -->
                    <Style x:Key="ModernToggleButton" TargetType="ToggleButton" BasedOn="{StaticResource MaterialDesignSwitchToggleButton}">
                        <Setter Property="Margin" Value="8,4"/>
                    </Style>
                    
                    <!-- Slider Style -->
                    <Style x:Key="ModernSlider" TargetType="Slider" BasedOn="{StaticResource MaterialDesignSlider}">
                        <Setter Property="Margin" Value="0,8"/>
                    </Style>
                    
                    <!-- List Box Style -->
                    <Style x:Key="ModernListBox" TargetType="ListBox" BasedOn="{StaticResource MaterialDesignListBox}">
                        <Setter Property="Margin" Value="8"/>
                        <Setter Property="MaxHeight" Value="200"/>
                    </Style>
                    
                    <!-- Expander Style -->
                    <Style x:Key="ModernExpander" TargetType="Expander" BasedOn="{StaticResource MaterialDesignExpander}">
                        <Setter Property="Margin" Value="0,8"/>
                    </Style>
                    
                    <!-- Chip Style -->
                    <Style x:Key="DeviceChip" TargetType="materialDesign:Chip">
                        <Setter Property="Margin" Value="4,2"/>
                        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
                    </Style>
                    
                    <!-- Status Text Styles -->
                    <Style x:Key="SuccessText" TargetType="TextBlock">
                        <Setter Property="Foreground" Value="#4CAF50"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>
                    
                    <Style x:Key="ErrorText" TargetType="TextBlock">
                        <Setter Property="Foreground" Value="#F44336"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>
                    
                    <Style x:Key="WarningText" TargetType="TextBlock">
                        <Setter Property="Foreground" Value="#FF9800"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>
                    
                    <Style x:Key="InfoText" TargetType="TextBlock">
                        <Setter Property="Foreground" Value="#2196F3"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
