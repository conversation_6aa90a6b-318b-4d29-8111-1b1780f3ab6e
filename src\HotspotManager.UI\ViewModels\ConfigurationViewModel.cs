using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using HotspotManager.Core.Services;
using HotspotManager.Core.Models;

namespace HotspotManager.UI.ViewModels;

/// <summary>
/// Configuration view model for hotspot settings
/// </summary>
public partial class ConfigurationViewModel : ObservableObject
{
    private readonly IHotspotService _hotspotService;
    private readonly ILogger<ConfigurationViewModel> _logger;

    [ObservableProperty]
    private HotspotConfiguration _configuration = new();

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string _errorMessage = string.Empty;

    public ConfigurationViewModel(IHotspotService hotspotService, ILogger<ConfigurationViewModel> logger)
    {
        _hotspotService = hotspotService;
        _logger = logger;
        
        _ = LoadConfigurationAsync();
    }

    private async Task LoadConfigurationAsync()
    {
        try
        {
            IsLoading = true;
            var config = await _hotspotService.GetConfigurationAsync();
            Configuration = config ?? new HotspotConfiguration();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading configuration");
            ErrorMessage = $"Load error: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task SaveConfigurationAsync()
    {
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;
            
            await _hotspotService.UpdateConfigurationAsync(Configuration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving configuration");
            ErrorMessage = $"Save error: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }
}
