using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using HotspotManager.Core.Services;
using HotspotManager.Core.Models;

namespace HotspotManager.UI.ViewModels;

/// <summary>
/// Dashboard view model showing hotspot overview and quick actions
/// </summary>
public partial class DashboardViewModel : ObservableObject
{
    private readonly IHotspotService _hotspotService;
    private readonly ILogger<DashboardViewModel> _logger;
    private readonly Timer _refreshTimer;

    [ObservableProperty]
    private HotspotStatus _status = new();

    [ObservableProperty]
    private NetworkStatistics _statistics = new();

    [ObservableProperty]
    private ObservableCollection<ConnectedDevice> _connectedDevices = new();

    [ObservableProperty]
    private ObservableCollection<HotspotEvent> _recentEvents = new();

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string _errorMessage = string.Empty;

    [ObservableProperty]
    private string _uptimeText = "00:00:00";

    [ObservableProperty]
    private double _dataUsageGB;

    [ObservableProperty]
    private double _currentSpeedMbps;

    [ObservableProperty]
    private int _totalConnections;

    [ObservableProperty]
    private string _networkAdapterName = "Unknown";

    [ObservableProperty]
    private string _ipAddress = "Not assigned";

    [ObservableProperty]
    private int? _channel;

    public DashboardViewModel(IHotspotService hotspotService, ILogger<DashboardViewModel> logger)
    {
        _hotspotService = hotspotService;
        _logger = logger;

        // Subscribe to events
        _hotspotService.StatusChanged += OnStatusChanged;
        _hotspotService.DeviceConnectionChanged += OnDeviceConnectionChanged;
        _hotspotService.StatisticsUpdated += OnStatisticsUpdated;
        _hotspotService.HotspotEventOccurred += OnHotspotEventOccurred;

        // Start refresh timer
        _refreshTimer = new Timer(RefreshCallback, null, TimeSpan.Zero, TimeSpan.FromSeconds(5));
    }

    private async void RefreshCallback(object? state)
    {
        try
        {
            await RefreshDataAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in refresh callback");
        }
    }

    [RelayCommand]
    private async Task RefreshDataAsync()
    {
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            // Get status
            Status = await _hotspotService.GetStatusAsync();

            // Get statistics
            Statistics = await _hotspotService.GetNetworkStatisticsAsync();

            // Get connected devices
            var devices = await _hotspotService.GetConnectedDevicesAsync();
            App.Current.Dispatcher.Invoke(() =>
            {
                ConnectedDevices.Clear();
                foreach (var device in devices)
                {
                    ConnectedDevices.Add(device);
                }
            });

            // Get recent events
            var events = await _hotspotService.GetEventsAsync(DateTime.UtcNow.AddHours(-1));
            App.Current.Dispatcher.Invoke(() =>
            {
                RecentEvents.Clear();
                foreach (var evt in events.Take(10))
                {
                    RecentEvents.Add(evt);
                }
            });

            // Update calculated properties
            UpdateCalculatedProperties();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing dashboard data");
            ErrorMessage = $"Refresh error: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void UpdateCalculatedProperties()
    {
        // Update uptime
        if (Status.StartedAt.HasValue)
        {
            var uptime = DateTime.UtcNow - Status.StartedAt.Value;
            UptimeText = uptime.ToString(@"hh\:mm\:ss");
        }
        else
        {
            UptimeText = "00:00:00";
        }

        // Update data usage
        DataUsageGB = Math.Round((Statistics.TotalBytesReceived + Statistics.TotalBytesSent) / (1024.0 * 1024.0 * 1024.0), 2);

        // Update current speed
        CurrentSpeedMbps = Math.Round(Statistics.CurrentDownloadSpeedMbps + Statistics.CurrentUploadSpeedMbps, 1);

        // Update total connections
        TotalConnections = ConnectedDevices.Count;

        // Update network info
        NetworkAdapterName = Status.NetworkAdapterName ?? "Unknown";
        IpAddress = Status.IPAddress ?? "Not assigned";
        Channel = Status.Channel;
    }

    [RelayCommand]
    private async Task StartHotspotAsync()
    {
        try
        {
            var config = await _hotspotService.GetConfigurationAsync() ?? new HotspotConfiguration
            {
                SSID = "Windows-Hotspot",
                Password = "HotspotPass123",
                MaxConnections = 8,
                Security = SecurityType.WPA2_PSK
            };

            await _hotspotService.StartHotspotAsync(config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting hotspot from dashboard");
            ErrorMessage = $"Start error: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task StopHotspotAsync()
    {
        try
        {
            await _hotspotService.StopHotspotAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping hotspot from dashboard");
            ErrorMessage = $"Stop error: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task RestartHotspotAsync()
    {
        try
        {
            await _hotspotService.RestartHotspotAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error restarting hotspot from dashboard");
            ErrorMessage = $"Restart error: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task DisconnectDeviceAsync(ConnectedDevice device)
    {
        try
        {
            if (device != null)
            {
                await _hotspotService.DisconnectDeviceAsync(device.MacAddress);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disconnecting device {MacAddress}", device?.MacAddress);
            ErrorMessage = $"Disconnect error: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task BlockDeviceAsync(ConnectedDevice device)
    {
        try
        {
            if (device != null)
            {
                await _hotspotService.BlockDeviceAsync(device.MacAddress, "Blocked from dashboard");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error blocking device {MacAddress}", device?.MacAddress);
            ErrorMessage = $"Block error: {ex.Message}";
        }
    }

    [RelayCommand]
    private void ClearError()
    {
        ErrorMessage = string.Empty;
    }

    private void OnStatusChanged(object? sender, HotspotStatusChangedEventArgs e)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            Status = e.Status;
            UpdateCalculatedProperties();
        });
    }

    private void OnDeviceConnectionChanged(object? sender, DeviceConnectionEventArgs e)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            if (e.IsConnected)
            {
                ConnectedDevices.Add(e.Device);
            }
            else
            {
                var existingDevice = ConnectedDevices.FirstOrDefault(d => d.MacAddress == e.Device.MacAddress);
                if (existingDevice != null)
                {
                    ConnectedDevices.Remove(existingDevice);
                }
            }

            UpdateCalculatedProperties();
        });
    }

    private void OnStatisticsUpdated(object? sender, NetworkStatisticsEventArgs e)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            Statistics = e.Statistics;
            UpdateCalculatedProperties();
        });
    }

    private void OnHotspotEventOccurred(object? sender, HotspotEventArgs e)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            RecentEvents.Insert(0, e.Event);
            
            // Keep only last 10 events
            while (RecentEvents.Count > 10)
            {
                RecentEvents.RemoveAt(RecentEvents.Count - 1);
            }
        });
    }

    public void Dispose()
    {
        _refreshTimer?.Dispose();
    }
}
