using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using HotspotManager.Core.Services;
using HotspotManager.Core.Models;

namespace HotspotManager.UI.ViewModels;

/// <summary>
/// Device management view model
/// </summary>
public partial class DeviceManagementViewModel : ObservableObject
{
    private readonly IHotspotService _hotspotService;
    private readonly ILogger<DeviceManagementViewModel> _logger;

    [ObservableProperty]
    private ObservableCollection<ConnectedDevice> _connectedDevices = new();

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string _errorMessage = string.Empty;

    public DeviceManagementViewModel(IHotspotService hotspotService, ILogger<DeviceManagementViewModel> logger)
    {
        _hotspotService = hotspotService;
        _logger = logger;
        
        _ = LoadDevicesAsync();
    }

    private async Task LoadDevicesAsync()
    {
        try
        {
            IsLoading = true;
            var devices = await _hotspotService.GetConnectedDevicesAsync();
            
            App.Current.Dispatcher.Invoke(() =>
            {
                ConnectedDevices.Clear();
                foreach (var device in devices)
                {
                    ConnectedDevices.Add(device);
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading devices");
            ErrorMessage = $"Load error: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    [RelayCommand]
    private async Task RefreshDevicesAsync()
    {
        await LoadDevicesAsync();
    }
}
