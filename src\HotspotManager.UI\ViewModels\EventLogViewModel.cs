using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using HotspotManager.Core.Services;
using HotspotManager.Core.Models;

namespace HotspotManager.UI.ViewModels;

/// <summary>
/// Event log view model
/// </summary>
public partial class EventLogViewModel : ObservableObject, IDisposable
{
    private readonly IHotspotService _hotspotService;
    private readonly ILogger<EventLogViewModel> _logger;

    [ObservableProperty]
    private ObservableCollection<HotspotEvent> _events = new();

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string _errorMessage = string.Empty;

    public EventLogViewModel(IHotspotService hotspotService, ILogger<EventLogViewModel> logger)
    {
        _hotspotService = hotspotService;
        _logger = logger;
        
        _hotspotService.HotspotEventOccurred += OnHotspotEventOccurred;
        _ = LoadEventsAsync();
    }

    private async Task LoadEventsAsync()
    {
        try
        {
            IsLoading = true;
            var events = await _hotspotService.GetEventsAsync();
            
            App.Current.Dispatcher.Invoke(() =>
            {
                Events.Clear();
                foreach (var evt in events)
                {
                    Events.Add(evt);
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error loading events");
            ErrorMessage = $"Load error: {ex.Message}";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private void OnHotspotEventOccurred(object? sender, HotspotEventArgs e)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            Events.Insert(0, e.Event);
        });
    }

    [RelayCommand]
    private async Task RefreshEventsAsync()
    {
        await LoadEventsAsync();
    }

    [RelayCommand]
    private async Task ClearEventsAsync()
    {
        try
        {
            await _hotspotService.ClearEventLogAsync();
            Events.Clear();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing events");
            ErrorMessage = $"Clear error: {ex.Message}";
        }
    }

    public void Dispose()
    {
        _hotspotService.HotspotEventOccurred -= OnHotspotEventOccurred;
    }
}
