using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Extensions.Logging;
using HotspotManager.Core.Services;
using HotspotManager.Core.Models;

namespace HotspotManager.UI.ViewModels;

/// <summary>
/// Main window view model
/// </summary>
public partial class MainWindowViewModel : ObservableObject
{
    private readonly IHotspotService _hotspotService;
    private readonly ILogger<MainWindowViewModel> _logger;

    [ObservableProperty]
    private string _title = "Windows Mobile Hotspot Manager";

    [ObservableProperty]
    private bool _isHotspotActive;

    [ObservableProperty]
    private bool _isHotspotAvailable;

    [ObservableProperty]
    private string _statusText = "Checking...";

    [ObservableProperty]
    private int _connectedDeviceCount;

    [ObservableProperty]
    private string _currentSSID = string.Empty;

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string _errorMessage = string.Empty;

    [ObservableProperty]
    private int _selectedTabIndex;

    // Child ViewModels
    public DashboardViewModel DashboardViewModel { get; }
    public ConfigurationViewModel ConfigurationViewModel { get; }
    public DeviceManagementViewModel DeviceManagementViewModel { get; }
    public StatisticsViewModel StatisticsViewModel { get; }
    public EventLogViewModel EventLogViewModel { get; }
    public SettingsViewModel SettingsViewModel { get; }

    public MainWindowViewModel(
        IHotspotService hotspotService,
        ILogger<MainWindowViewModel> logger,
        DashboardViewModel dashboardViewModel,
        ConfigurationViewModel configurationViewModel,
        DeviceManagementViewModel deviceManagementViewModel,
        StatisticsViewModel statisticsViewModel,
        EventLogViewModel eventLogViewModel,
        SettingsViewModel settingsViewModel)
    {
        _hotspotService = hotspotService;
        _logger = logger;

        DashboardViewModel = dashboardViewModel;
        ConfigurationViewModel = configurationViewModel;
        DeviceManagementViewModel = deviceManagementViewModel;
        StatisticsViewModel = statisticsViewModel;
        EventLogViewModel = eventLogViewModel;
        SettingsViewModel = settingsViewModel;

        // Subscribe to hotspot service events
        _hotspotService.StatusChanged += OnHotspotStatusChanged;
        _hotspotService.DeviceConnectionChanged += OnDeviceConnectionChanged;
        _hotspotService.HotspotEventOccurred += OnHotspotEventOccurred;

        // Initialize
        _ = InitializeAsync();
    }

    private async Task InitializeAsync()
    {
        try
        {
            IsLoading = true;
            ErrorMessage = string.Empty;

            // Check if hotspot is available
            IsHotspotAvailable = await _hotspotService.IsHotspotAvailableAsync();

            if (!IsHotspotAvailable)
            {
                StatusText = "Mobile Hotspot not available";
                ErrorMessage = "Mobile Hotspot functionality is not available on this system. Please check if your wireless adapter supports hosted networks.";
                return;
            }

            // Get current status
            await UpdateStatusAsync();

            StatusText = IsHotspotActive ? "Active" : "Inactive";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing main window");
            ErrorMessage = $"Initialization error: {ex.Message}";
            StatusText = "Error";
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task UpdateStatusAsync()
    {
        try
        {
            var status = await _hotspotService.GetStatusAsync();
            
            IsHotspotActive = status.IsActive;
            ConnectedDeviceCount = status.ConnectedDeviceCount;
            
            if (status.IsActive)
            {
                var config = await _hotspotService.GetConfigurationAsync();
                CurrentSSID = config?.SSID ?? "Unknown";
            }
            else
            {
                CurrentSSID = string.Empty;
            }

            if (!string.IsNullOrEmpty(status.ErrorMessage))
            {
                ErrorMessage = status.ErrorMessage;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating status");
            ErrorMessage = $"Status update error: {ex.Message}";
        }
    }

    private void OnHotspotStatusChanged(object? sender, HotspotStatusChangedEventArgs e)
    {
        App.Current.Dispatcher.Invoke(async () =>
        {
            await UpdateStatusAsync();
            StatusText = IsHotspotActive ? "Active" : "Inactive";
        });
    }

    private void OnDeviceConnectionChanged(object? sender, DeviceConnectionEventArgs e)
    {
        App.Current.Dispatcher.Invoke(async () =>
        {
            await UpdateStatusAsync();
        });
    }

    private void OnHotspotEventOccurred(object? sender, HotspotEventArgs e)
    {
        App.Current.Dispatcher.Invoke(() =>
        {
            // Handle events if needed
            _logger.LogInformation("Hotspot event: {EventType} - {Message}", e.Event.EventType, e.Event.Message);
        });
    }

    [RelayCommand]
    private async Task QuickStartAsync()
    {
        try
        {
            if (IsHotspotActive)
            {
                await _hotspotService.StopHotspotAsync();
            }
            else
            {
                // Use default configuration or last saved configuration
                var config = await _hotspotService.GetConfigurationAsync() ?? new HotspotConfiguration
                {
                    SSID = "Windows-Hotspot",
                    Password = "HotspotPass123",
                    MaxConnections = 8,
                    Security = SecurityType.WPA2_PSK
                };

                await _hotspotService.StartHotspotAsync(config);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in quick start");
            ErrorMessage = $"Quick start error: {ex.Message}";
        }
    }

    [RelayCommand]
    private async Task RefreshAsync()
    {
        await UpdateStatusAsync();
    }

    [RelayCommand]
    private void ShowDashboard()
    {
        SelectedTabIndex = 0;
    }

    [RelayCommand]
    private void ShowConfiguration()
    {
        SelectedTabIndex = 1;
    }

    [RelayCommand]
    private void ShowDeviceManagement()
    {
        SelectedTabIndex = 2;
    }

    [RelayCommand]
    private void ShowStatistics()
    {
        SelectedTabIndex = 3;
    }

    [RelayCommand]
    private void ShowEventLog()
    {
        SelectedTabIndex = 4;
    }

    [RelayCommand]
    private void ShowSettings()
    {
        SelectedTabIndex = 5;
    }

    [RelayCommand]
    private void ClearError()
    {
        ErrorMessage = string.Empty;
    }

    [RelayCommand]
    private async Task ExitApplicationAsync()
    {
        try
        {
            // Stop hotspot if active
            if (IsHotspotActive)
            {
                await _hotspotService.StopHotspotAsync();
            }

            App.Current.Shutdown();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during application exit");
            App.Current.Shutdown();
        }
    }

    protected override void OnPropertyChanged(PropertyChangedEventArgs e)
    {
        base.OnPropertyChanged(e);

        // Update title based on status
        if (e.PropertyName == nameof(IsHotspotActive) || e.PropertyName == nameof(CurrentSSID))
        {
            if (IsHotspotActive && !string.IsNullOrEmpty(CurrentSSID))
            {
                Title = $"Hotspot Manager - {CurrentSSID} (Active)";
            }
            else
            {
                Title = "Windows Mobile Hotspot Manager";
            }
        }
    }
}
