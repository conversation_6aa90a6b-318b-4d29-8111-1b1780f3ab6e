using CommunityToolkit.Mvvm.ComponentModel;
using Microsoft.Extensions.Logging;
using HotspotManager.Core.Services;

namespace HotspotManager.UI.ViewModels;

/// <summary>
/// Settings view model
/// </summary>
public partial class SettingsViewModel : ObservableObject
{
    private readonly IHotspotService _hotspotService;
    private readonly ILogger<SettingsViewModel> _logger;

    [ObservableProperty]
    private bool _autoStartEnabled;

    [ObservableProperty]
    private bool _minimizeToTray;

    [ObservableProperty]
    private bool _showNotifications;

    [ObservableProperty]
    private string _logLevel = "Information";

    public SettingsViewModel(IHotspotService hotspotService, ILogger<SettingsViewModel> logger)
    {
        _hotspotService = hotspotService;
        _logger = logger;
    }
}
