using CommunityToolkit.Mvvm.ComponentModel;
using Microsoft.Extensions.Logging;
using HotspotManager.Core.Services;
using HotspotManager.Core.Models;

namespace HotspotManager.UI.ViewModels;

/// <summary>
/// Statistics view model
/// </summary>
public partial class StatisticsViewModel : ObservableObject, IDisposable
{
    private readonly IHotspotService _hotspotService;
    private readonly ILogger<StatisticsViewModel> _logger;
    private readonly Timer _refreshTimer;

    [ObservableProperty]
    private NetworkStatistics _statistics = new();

    [ObservableProperty]
    private bool _isLoading;

    [ObservableProperty]
    private string _errorMessage = string.Empty;

    public StatisticsViewModel(IHotspotService hotspotService, ILogger<StatisticsViewModel> logger)
    {
        _hotspotService = hotspotService;
        _logger = logger;
        
        _refreshTimer = new Timer(RefreshCallback, null, TimeSpan.Zero, TimeSpan.FromSeconds(10));
    }

    private async void RefreshCallback(object? state)
    {
        try
        {
            Statistics = await _hotspotService.GetNetworkStatisticsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing statistics");
        }
    }

    public void Dispose()
    {
        _refreshTimer?.Dispose();
    }
}
