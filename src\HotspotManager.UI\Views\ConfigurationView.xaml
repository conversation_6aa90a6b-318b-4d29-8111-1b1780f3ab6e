<UserControl x:Class="HotspotManager.UI.Views.ConfigurationView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="16">
            <StackPanel>
                <TextBlock Text="Configuration" 
                           Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                           Margin="0,0,0,16"/>
                
                <materialDesign:Card Style="{StaticResource InfoCard}">
                    <StackPanel>
                        <TextBlock Text="Hotspot Settings" Style="{StaticResource SectionHeader}"/>
                        
                        <TextBox Text="{Binding Configuration.SSID}"
                                 Style="{StaticResource ModernTextBox}"
                                 materialDesign:HintAssist.Hint="Network Name (SSID)"/>
                        
                        <PasswordBox x:Name="PasswordBox"
                                     Style="{StaticResource ModernPasswordBox}"
                                     materialDesign:HintAssist.Hint="Password"/>
                        
                        <ComboBox SelectedItem="{Binding Configuration.Security}"
                                  Style="{StaticResource ModernComboBox}"
                                  materialDesign:HintAssist.Hint="Security Type">
                            <ComboBoxItem Content="Open"/>
                            <ComboBoxItem Content="WPA2_PSK"/>
                            <ComboBoxItem Content="WPA3_SAE"/>
                        </ComboBox>
                        
                        <TextBox Text="{Binding Configuration.MaxConnections}"
                                 Style="{StaticResource NumericTextBox}"
                                 materialDesign:HintAssist.Hint="Max Connections (1-8)"/>
                        
                        <Button Style="{StaticResource PrimaryButton}"
                                Command="{Binding SaveConfigurationCommand}"
                                Content="Save Configuration"
                                HorizontalAlignment="Left"
                                Margin="0,16,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </Grid>
    </ScrollViewer>
</UserControl>
