<UserControl x:Class="HotspotManager.UI.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <TextBlock Grid.Row="0" 
                       Text="Dashboard" 
                       Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                       Margin="0,0,0,16"/>

            <!-- Status Cards -->
            <Grid Grid.Row="1" Margin="0,0,0,16">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Hotspot Status Card -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource MetricCard}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Wifi" 
                                                     VerticalAlignment="Center" 
                                                     Margin="0,0,8,0">
                                <materialDesign:PackIcon.Style>
                                    <Style TargetType="materialDesign:PackIcon">
                                        <Setter Property="Foreground" Value="#F44336"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Status.IsActive}" Value="True">
                                                <Setter Property="Foreground" Value="#4CAF50"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </materialDesign:PackIcon.Style>
                            </materialDesign:PackIcon>
                            <TextBlock>
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock" BasedOn="{StaticResource MetricValue}">
                                        <Setter Property="Text" Value="Inactive"/>
                                        <Setter Property="Foreground" Value="#F44336"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Status.IsActive}" Value="True">
                                                <Setter Property="Text" Value="Active"/>
                                                <Setter Property="Foreground" Value="#4CAF50"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </StackPanel>
                        <TextBlock Text="Hotspot Status" Style="{StaticResource MetricLabel}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Connected Devices Card -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource MetricCard}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Devices" 
                                                     VerticalAlignment="Center" 
                                                     Margin="0,0,8,0"
                                                     Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{Binding TotalConnections}" Style="{StaticResource MetricValue}"/>
                        </StackPanel>
                        <TextBlock Text="Connected Devices" Style="{StaticResource MetricLabel}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Data Usage Card -->
                <materialDesign:Card Grid.Column="2" Style="{StaticResource MetricCard}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Download" 
                                                     VerticalAlignment="Center" 
                                                     Margin="0,0,8,0"
                                                     Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="0.00 GB" Style="{StaticResource MetricValue}"/>
                        </StackPanel>
                        <TextBlock Text="Data Usage" Style="{StaticResource MetricLabel}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Uptime Card -->
                <materialDesign:Card Grid.Column="3" Style="{StaticResource MetricCard}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <materialDesign:PackIcon Kind="Clock" 
                                                     VerticalAlignment="Center" 
                                                     Margin="0,0,8,0"
                                                     Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                            <TextBlock Text="{Binding UptimeText}" Style="{StaticResource MetricValue}"/>
                        </StackPanel>
                        <TextBlock Text="Uptime" Style="{StaticResource MetricLabel}"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Quick Actions -->
            <materialDesign:Card Grid.Row="2" Style="{StaticResource InfoCard}" Margin="0,0,0,16">
                <StackPanel>
                    <TextBlock Text="Quick Actions" 
                               Style="{StaticResource SectionHeader}"/>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,8">
                        <Button Style="{StaticResource PrimaryButton}"
                                Command="{Binding StartHotspotCommand}"
                                Visibility="{Binding Status.IsActive, Converter={StaticResource InverseBooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Play" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="Start Hotspot"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource DangerButton}"
                                Command="{Binding StopHotspotCommand}"
                                Visibility="{Binding Status.IsActive, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Stop" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="Stop Hotspot"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource SecondaryButton}"
                                Command="{Binding RestartHotspotCommand}"
                                IsEnabled="{Binding Status.IsActive}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Restart" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="Restart"/>
                            </StackPanel>
                        </Button>

                        <Button Style="{StaticResource SecondaryButton}"
                                Command="{Binding RefreshDataCommand}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Refresh" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                <TextBlock Text="Refresh"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!-- Content Grid -->
            <Grid Grid.Row="3">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Connected Devices -->
                <materialDesign:Card Grid.Column="0" Style="{StaticResource InfoCard}" Margin="0,0,8,0">
                    <StackPanel>
                        <TextBlock Text="Connected Devices" Style="{StaticResource SectionHeader}"/>
                        
                        <DataGrid ItemsSource="{Binding ConnectedDevices}"
                                  Style="{StaticResource ModernDataGrid}"
                                  MaxHeight="300">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="Device" 
                                                    Binding="{Binding DeviceName}" 
                                                    Width="*"/>
                                <DataGridTextColumn Header="MAC Address" 
                                                    Binding="{Binding MacAddress}" 
                                                    Width="140"/>
                                <DataGridTextColumn Header="IP Address" 
                                                    Binding="{Binding IPAddress}" 
                                                    Width="100"/>
                                <DataGridTextColumn Header="Connected"
                                                    Binding="{Binding ConnectedAt}"
                                                    Width="80"/>
                                <DataGridTemplateColumn Header="Actions" Width="120">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        Command="{Binding DataContext.DisconnectDeviceCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                        CommandParameter="{Binding}"
                                                        ToolTip="Disconnect">
                                                    <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                                                </Button>
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        Command="{Binding DataContext.BlockDeviceCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                        CommandParameter="{Binding}"
                                                        ToolTip="Block">
                                                    <materialDesign:PackIcon Kind="Block" Width="16" Height="16"/>
                                                </Button>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>

                        <TextBlock Text="No devices connected" 
                                   HorizontalAlignment="Center"
                                   Margin="0,16"
                                   Style="{StaticResource MaterialDesignBody2TextBlock}"
                                   Foreground="{DynamicResource MaterialDesignBodyLight}"
                                   Visibility="{Binding ConnectedDevices.Count, Converter={StaticResource CountToVisibilityConverter}}"/>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Recent Events -->
                <materialDesign:Card Grid.Column="1" Style="{StaticResource InfoCard}" Margin="8,0,0,0">
                    <StackPanel>
                        <TextBlock Text="Recent Events" Style="{StaticResource SectionHeader}"/>
                        
                        <ListBox ItemsSource="{Binding RecentEvents}"
                                 Style="{StaticResource ModernListBox}">
                            <ListBox.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Margin="0,4">
                                        <TextBlock Text="{Binding Message}" 
                                                   TextWrapping="Wrap"
                                                   FontWeight="Medium"/>
                                        <TextBlock Text="{Binding Timestamp}"
                                                   FontSize="11"
                                                   Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ListBox.ItemTemplate>
                        </ListBox>

                        <TextBlock Text="No recent events" 
                                   HorizontalAlignment="Center"
                                   Margin="0,16"
                                   Style="{StaticResource MaterialDesignBody2TextBlock}"
                                   Foreground="{DynamicResource MaterialDesignBodyLight}"
                                   Visibility="{Binding RecentEvents.Count, Converter={StaticResource CountToVisibilityConverter}}"/>
                    </StackPanel>
                </materialDesign:Card>
            </Grid>

            <!-- Loading Overlay -->
            <Grid Grid.RowSpan="4" 
                  Background="#80000000"
                  Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                 Width="48" Height="48"
                                 IsIndeterminate="True"/>
                    <TextBlock Text="Loading..." 
                               Foreground="White"
                               Margin="0,16,0,0"
                               HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</UserControl>
