<UserControl x:Class="HotspotManager.UI.Views.DeviceManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <StackPanel Grid.Row="0">
            <TextBlock Text="Device Management" 
                       Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                       Margin="0,0,0,16"/>
            
            <Button Style="{StaticResource SecondaryButton}"
                    Command="{Binding RefreshDevicesCommand}"
                    Content="Refresh Devices"
                    HorizontalAlignment="Left"
                    Margin="0,0,0,16"/>
        </StackPanel>

        <materialDesign:Card Grid.Row="1" Style="{StaticResource InfoCard}">
            <StackPanel>
                <TextBlock Text="Connected Devices" Style="{StaticResource SectionHeader}"/>
                
                <DataGrid ItemsSource="{Binding ConnectedDevices}"
                          Style="{StaticResource ModernDataGrid}">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Device Name" 
                                            Binding="{Binding DeviceName}" 
                                            Width="*"/>
                        <DataGridTextColumn Header="MAC Address" 
                                            Binding="{Binding MacAddress}" 
                                            Width="140"/>
                        <DataGridTextColumn Header="IP Address" 
                                            Binding="{Binding IPAddress}" 
                                            Width="100"/>
                        <DataGridTextColumn Header="Connected At" 
                                            Binding="{Binding ConnectedAt, StringFormat='{0:yyyy-MM-dd HH:mm:ss}'}" 
                                            Width="140"/>
                        <DataGridTextColumn Header="Data Usage" 
                                            Binding="{Binding BytesReceived, StringFormat='{0:N0} bytes'}" 
                                            Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>

                <TextBlock Text="No devices connected" 
                           HorizontalAlignment="Center"
                           Margin="0,32"
                           Style="{StaticResource MaterialDesignBody1TextBlock}"
                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                           Visibility="{Binding ConnectedDevices.Count, Converter={StaticResource CountToVisibilityConverter}}"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
