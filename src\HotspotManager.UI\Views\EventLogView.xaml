<UserControl x:Class="HotspotManager.UI.Views.EventLogView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0"
                   Text="Event Log" 
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                   Margin="0,0,0,16"/>
        
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,16">
            <Button Style="{StaticResource SecondaryButton}"
                    Command="{Binding RefreshEventsCommand}"
                    Content="Refresh"/>
            
            <Button Style="{StaticResource DangerButton}"
                    Command="{Binding ClearEventsCommand}"
                    Content="Clear Log"/>
        </StackPanel>

        <materialDesign:Card Grid.Row="2" Style="{StaticResource InfoCard}">
            <StackPanel>
                <DataGrid ItemsSource="{Binding Events}"
                          Style="{StaticResource ModernDataGrid}">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Time" 
                                            Binding="{Binding Timestamp, StringFormat='{0:yyyy-MM-dd HH:mm:ss}'}" 
                                            Width="140"/>
                        <DataGridTextColumn Header="Type" 
                                            Binding="{Binding EventType}" 
                                            Width="120"/>
                        <DataGridTextColumn Header="Message" 
                                            Binding="{Binding Message}" 
                                            Width="*"/>
                        <DataGridTextColumn Header="Device" 
                                            Binding="{Binding DeviceMacAddress}" 
                                            Width="140"/>
                    </DataGrid.Columns>
                </DataGrid>

                <TextBlock Text="No events to display" 
                           HorizontalAlignment="Center"
                           Margin="0,32"
                           Style="{StaticResource MaterialDesignBody1TextBlock}"
                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                           Visibility="{Binding Events.Count, Converter={StaticResource CountToVisibilityConverter}}"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>
