<Window x:Class="HotspotManager.UI.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:viewModels="clr-namespace:HotspotManager.UI.ViewModels"
        xmlns:views="clr-namespace:HotspotManager.UI.Views"
        mc:Ignorable="d"
        Title="{Binding Title}"
        Height="800" Width="1200"
        MinHeight="600" MinWidth="900"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.DataContext>
        <viewModels:MainWindowViewModel/>
    </Window.DataContext>

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Title Bar -->
            <materialDesign:ColorZone Grid.Row="0" 
                                      Mode="PrimaryMid" 
                                      Padding="16,8">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- App Icon and Title -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Wifi" 
                                                 Width="24" Height="24" 
                                                 VerticalAlignment="Center"
                                                 Margin="0,0,8,0"/>
                        <TextBlock Text="{Binding Title}" 
                                   FontSize="18" 
                                   FontWeight="Medium"
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- Status Indicator -->
                    <StackPanel Grid.Column="1" 
                                Orientation="Horizontal" 
                                HorizontalAlignment="Center"
                                VerticalAlignment="Center">
                        <Ellipse Width="12" Height="12" 
                                 Margin="0,0,8,0"
                                 VerticalAlignment="Center">
                            <Ellipse.Style>
                                <Style TargetType="Ellipse">
                                    <Setter Property="Fill" Value="#F44336"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsHotspotActive}" Value="True">
                                            <Setter Property="Fill" Value="#4CAF50"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Ellipse.Style>
                        </Ellipse>
                        <TextBlock Text="{Binding StatusText}" 
                                   FontWeight="Medium"
                                   VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding ConnectedDeviceCount, StringFormat=' ({0} devices)'}" 
                                   VerticalAlignment="Center"
                                   Margin="4,0,0,0">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsHotspotActive}" Value="True">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </StackPanel>

                    <!-- Quick Actions -->
                    <StackPanel Grid.Column="2" 
                                Orientation="Horizontal">
                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                Command="{Binding QuickStartCommand}"
                                ToolTip="{Binding IsHotspotActive, Converter={StaticResource BoolToStringConverter}, ConverterParameter='Stop Hotspot|Start Hotspot'}"
                                Margin="4,0">
                            <materialDesign:PackIcon>
                                <materialDesign:PackIcon.Style>
                                    <Style TargetType="materialDesign:PackIcon">
                                        <Setter Property="Kind" Value="Play"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding IsHotspotActive}" Value="True">
                                                <Setter Property="Kind" Value="Stop"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </materialDesign:PackIcon.Style>
                            </materialDesign:PackIcon>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                Command="{Binding RefreshCommand}"
                                ToolTip="Refresh Status"
                                Margin="4,0">
                            <materialDesign:PackIcon Kind="Refresh" Foreground="White"/>
                        </Button>

                        <Button Style="{StaticResource MaterialDesignIconButton}"
                                Command="{Binding ExitApplicationCommand}"
                                ToolTip="Exit Application"
                                Margin="4,0">
                            <materialDesign:PackIcon Kind="Close" Foreground="White"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </materialDesign:ColorZone>

            <!-- Error Banner -->
            <materialDesign:Card Grid.Row="1" 
                                 Background="#F44336"
                                 Margin="8,4"
                                 Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}">
                <Grid Margin="16,8">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <materialDesign:PackIcon Grid.Column="0" 
                                             Kind="AlertCircle" 
                                             Foreground="White"
                                             VerticalAlignment="Center"
                                             Margin="0,0,8,0"/>

                    <TextBlock Grid.Column="1" 
                               Text="{Binding ErrorMessage}"
                               Foreground="White"
                               TextWrapping="Wrap"
                               VerticalAlignment="Center"/>

                    <Button Grid.Column="2"
                            Style="{StaticResource MaterialDesignIconButton}"
                            Command="{Binding ClearErrorCommand}"
                            Margin="8,0,0,0">
                        <materialDesign:PackIcon Kind="Close" Foreground="White"/>
                    </Button>
                </Grid>
            </materialDesign:Card>

            <!-- Main Content -->
            <TabControl Grid.Row="2" 
                        Style="{StaticResource ModernTabControl}"
                        SelectedIndex="{Binding SelectedTabIndex}"
                        Margin="8">
                
                <!-- Dashboard Tab -->
                <TabItem Header="Dashboard">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ViewDashboard" 
                                                         VerticalAlignment="Center" 
                                                         Margin="0,0,4,0"/>
                                <TextBlock Text="Dashboard" VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>
                    <views:DashboardView DataContext="{Binding DashboardViewModel}"/>
                </TabItem>

                <!-- Configuration Tab -->
                <TabItem Header="Configuration">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Settings" 
                                                         VerticalAlignment="Center" 
                                                         Margin="0,0,4,0"/>
                                <TextBlock Text="Configuration" VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>
                    <views:ConfigurationView DataContext="{Binding ConfigurationViewModel}"/>
                </TabItem>

                <!-- Device Management Tab -->
                <TabItem Header="Devices">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Devices" 
                                                         VerticalAlignment="Center" 
                                                         Margin="0,0,4,0"/>
                                <TextBlock Text="Devices" VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>
                    <views:DeviceManagementView DataContext="{Binding DeviceManagementViewModel}"/>
                </TabItem>

                <!-- Statistics Tab -->
                <TabItem Header="Statistics">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ChartLine" 
                                                         VerticalAlignment="Center" 
                                                         Margin="0,0,4,0"/>
                                <TextBlock Text="Statistics" VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>
                    <views:StatisticsView DataContext="{Binding StatisticsViewModel}"/>
                </TabItem>

                <!-- Event Log Tab -->
                <TabItem Header="Events">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="FormatListBulleted" 
                                                         VerticalAlignment="Center" 
                                                         Margin="0,0,4,0"/>
                                <TextBlock Text="Events" VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>
                    <views:EventLogView DataContext="{Binding EventLogViewModel}"/>
                </TabItem>

                <!-- Settings Tab -->
                <TabItem Header="Settings">
                    <TabItem.HeaderTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Cog" 
                                                         VerticalAlignment="Center" 
                                                         Margin="0,0,4,0"/>
                                <TextBlock Text="Settings" VerticalAlignment="Center"/>
                            </StackPanel>
                        </DataTemplate>
                    </TabItem.HeaderTemplate>
                    <views:SettingsView DataContext="{Binding SettingsViewModel}"/>
                </TabItem>
            </TabControl>

            <!-- Status Bar -->
            <materialDesign:ColorZone Grid.Row="3" 
                                      Mode="PrimaryDark" 
                                      Padding="16,4">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0" 
                               Text="{Binding CurrentSSID, StringFormat='SSID: {0}'}"
                               VerticalAlignment="Center"
                               Foreground="White">
                        <TextBlock.Style>
                            <Style TargetType="TextBlock">
                                <Setter Property="Visibility" Value="Collapsed"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding IsHotspotActive}" Value="True">
                                        <Setter Property="Visibility" Value="Visible"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </TextBlock.Style>
                    </TextBlock>

                    <StackPanel Grid.Column="1" 
                                Orientation="Horizontal">
                        <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                     Width="16" Height="16"
                                     IsIndeterminate="True"
                                     Margin="0,0,8,0"
                                     Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                        
                        <TextBlock Text="{Binding Source={x:Static Application.Current}, Path=MainWindow.Title}"
                                   VerticalAlignment="Center"
                                   Foreground="White"
                                   FontSize="11"/>
                    </StackPanel>
                </Grid>
            </materialDesign:ColorZone>
        </Grid>
    </materialDesign:DialogHost>
</Window>
