using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using HotspotManager.UI.ViewModels;

namespace HotspotManager.UI.Views;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
        
        // Set DataContext from DI container
        if (App.Current is App app && app.Host != null)
        {
            DataContext = app.Host.Services.GetRequiredService<MainWindowViewModel>();
        }
    }

    protected override void OnClosed(EventArgs e)
    {
        // Dispose ViewModels if they implement IDisposable
        if (DataContext is MainWindowViewModel viewModel)
        {
            if (viewModel.DashboardViewModel is IDisposable dashboardDisposable)
                dashboardDisposable.Dispose();
            
            if (viewModel.StatisticsViewModel is IDisposable statisticsDisposable)
                statisticsDisposable.Dispose();
            
            if (viewModel.EventLogViewModel is IDisposable eventLogDisposable)
                eventLogDisposable.Dispose();
        }

        base.OnClosed(e);
    }
}
