<UserControl x:Class="HotspotManager.UI.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <Grid Margin="16">
        <StackPanel>
            <TextBlock Text="Settings" 
                       Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                       Margin="0,0,0,16"/>
            
            <materialDesign:Card Style="{StaticResource InfoCard}">
                <StackPanel>
                    <TextBlock Text="Application Settings" Style="{StaticResource SectionHeader}"/>
                    
                    <CheckBox Content="Auto-start hotspot on system boot"
                              IsChecked="{Binding AutoStartEnabled}"
                              Margin="0,8"/>
                    
                    <CheckBox Content="Minimize to system tray"
                              IsChecked="{Binding MinimizeToTray}"
                              Margin="0,8"/>
                    
                    <CheckBox Content="Show notifications"
                              IsChecked="{Binding ShowNotifications}"
                              Margin="0,8"/>
                    
                    <ComboBox SelectedItem="{Binding LogLevel}"
                              Style="{StaticResource ModernComboBox}"
                              materialDesign:HintAssist.Hint="Log Level"
                              Margin="0,16,0,0">
                        <ComboBoxItem Content="Debug"/>
                        <ComboBoxItem Content="Information"/>
                        <ComboBoxItem Content="Warning"/>
                        <ComboBoxItem Content="Error"/>
                    </ComboBox>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </Grid>
</UserControl>
