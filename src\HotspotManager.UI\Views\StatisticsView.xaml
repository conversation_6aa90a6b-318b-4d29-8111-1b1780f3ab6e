<UserControl x:Class="HotspotManager.UI.Views.StatisticsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <Grid Margin="16">
        <StackPanel>
            <TextBlock Text="Statistics" 
                       Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                       Margin="0,0,0,16"/>
            
            <materialDesign:Card Style="{StaticResource InfoCard}">
                <StackPanel>
                    <TextBlock Text="Network Statistics" Style="{StaticResource SectionHeader}"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Total Bytes Received:" Margin="0,8"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" Text="0 bytes" Margin="0,8"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Total Bytes Sent:" Margin="0,8"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" Text="0 bytes" Margin="0,8"/>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Current Download Speed:" Margin="0,8"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" Text="0.00 Mbps" Margin="0,8"/>
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Current Upload Speed:" Margin="0,8"/>
                        <TextBlock Grid.Row="3" Grid.Column="1" Text="0.00 Mbps" Margin="0,8"/>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
    </Grid>
</UserControl>
