{"format": 1, "restore": {"D:\\workspace\\.system\\hotspot\\src\\HotspotManager.UI\\HotspotManager.UI.csproj": {}}, "projects": {"D:\\workspace\\.system\\hotspot\\src\\HotspotManager.Core\\HotspotManager.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\workspace\\.system\\hotspot\\src\\HotspotManager.Core\\HotspotManager.Core.csproj", "projectName": "HotspotManager.Core", "projectPath": "D:\\workspace\\.system\\hotspot\\src\\HotspotManager.Core\\HotspotManager.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\workspace\\.system\\hotspot\\src\\HotspotManager.Core\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Win32.Registry": {"target": "Package", "version": "[5.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Management": {"target": "Package", "version": "[8.0.0, )"}, "System.ServiceProcess.ServiceController": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.19, 8.0.19]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}, "D:\\workspace\\.system\\hotspot\\src\\HotspotManager.UI\\HotspotManager.UI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\workspace\\.system\\hotspot\\src\\HotspotManager.UI\\HotspotManager.UI.csproj", "projectName": "HotspotManager.UI", "projectPath": "D:\\workspace\\.system\\hotspot\\src\\HotspotManager.UI\\HotspotManager.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\workspace\\.system\\hotspot\\src\\HotspotManager.UI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\workspace\\.system\\hotspot\\src\\HotspotManager.Core\\HotspotManager.Core.csproj": {"projectPath": "D:\\workspace\\.system\\hotspot\\src\\HotspotManager.Core\\HotspotManager.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "Hardcodet.NotifyIcon.Wpf": {"target": "Package", "version": "[1.1.0, )"}, "LiveCharts.Wpf": {"target": "Package", "version": "[0.9.7, )"}, "MaterialDesignThemes": {"target": "Package", "version": "[4.9.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.19, 8.0.19]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}}}}