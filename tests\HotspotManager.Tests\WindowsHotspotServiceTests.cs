using Xunit;
using Moq;
using Microsoft.Extensions.Logging;
using FluentAssertions;
using HotspotManager.Core.Services;
using HotspotManager.Core.Models;

namespace HotspotManager.Tests;

/// <summary>
/// Unit tests for WindowsHotspotService
/// </summary>
public class WindowsHotspotServiceTests : IDisposable
{
    private readonly Mock<ILogger<WindowsHotspotService>> _mockLogger;
    private readonly WindowsHotspotService _service;

    public WindowsHotspotServiceTests()
    {
        _mockLogger = new Mock<ILogger<WindowsHotspotService>>();
        _service = new WindowsHotspotService(_mockLogger.Object);
    }

    [Fact]
    public async Task IsHotspotAvailableAsync_ShouldReturnBoolean()
    {
        // Act
        var result = await _service.IsHotspotAvailableAsync();

        // Assert
        result.Should().BeOfType<bool>();
    }

    [Fact]
    public async Task GetStatusAsync_ShouldReturnHotspotStatus()
    {
        // Act
        var result = await _service.GetStatusAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<HotspotStatus>();
    }

    [Fact]
    public async Task GetConnectedDevicesAsync_ShouldReturnDeviceList()
    {
        // Act
        var result = await _service.GetConnectedDevicesAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<List<ConnectedDevice>>();
    }

    [Fact]
    public async Task GetNetworkStatisticsAsync_ShouldReturnStatistics()
    {
        // Act
        var result = await _service.GetNetworkStatisticsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<NetworkStatistics>();
        result.LastUpdated.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public async Task GetEventsAsync_ShouldReturnEventList()
    {
        // Act
        var result = await _service.GetEventsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<List<HotspotEvent>>();
    }

    [Fact]
    public async Task GetAvailableAdaptersAsync_ShouldReturnAdapterList()
    {
        // Act
        var result = await _service.GetAvailableAdaptersAsync();

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<List<NetworkAdapter>>();
    }

    [Fact]
    public void HotspotConfiguration_ShouldValidateSSID()
    {
        // Arrange
        var config = new HotspotConfiguration();

        // Act & Assert
        config.SSID = "ValidSSID";
        config.SSID.Should().Be("ValidSSID");

        config.SSID = "";
        config.SSID.Should().Be("");
    }

    [Fact]
    public void HotspotConfiguration_ShouldValidatePassword()
    {
        // Arrange
        var config = new HotspotConfiguration();

        // Act & Assert
        config.Password = "ValidPassword123";
        config.Password.Should().Be("ValidPassword123");

        config.Password = "short";
        config.Password.Should().Be("short");
    }

    [Fact]
    public void HotspotConfiguration_ShouldHaveDefaultValues()
    {
        // Arrange & Act
        var config = new HotspotConfiguration();

        // Assert
        config.SSID.Should().Be("Windows-Hotspot");
        config.MaxConnections.Should().Be(8);
        config.Security.Should().Be(SecurityType.WPA2_PSK);
        config.Channel.Should().Be(6);
        config.IsHidden.Should().BeFalse();
        config.AutoStart.Should().BeFalse();
        config.GuestNetworkIsolation.Should().BeTrue();
        config.CreatedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Fact]
    public void ConnectedDevice_ShouldInitializeCorrectly()
    {
        // Arrange & Act
        var device = new ConnectedDevice
        {
            MacAddress = "AA:BB:CC:DD:EE:FF",
            DeviceName = "TestDevice",
            IPAddress = "*************"
        };

        // Assert
        device.MacAddress.Should().Be("AA:BB:CC:DD:EE:FF");
        device.DeviceName.Should().Be("TestDevice");
        device.IPAddress.Should().Be("*************");
        device.DeviceType.Should().Be(DeviceType.Unknown);
        device.IsBlocked.Should().BeFalse();
    }

    [Fact]
    public void NetworkStatistics_ShouldInitializeCorrectly()
    {
        // Arrange & Act
        var stats = new NetworkStatistics
        {
            TotalBytesReceived = 1024,
            TotalBytesSent = 2048,
            CurrentDownloadSpeedMbps = 10.5,
            CurrentUploadSpeedMbps = 5.2
        };

        // Assert
        stats.TotalBytesReceived.Should().Be(1024);
        stats.TotalBytesSent.Should().Be(2048);
        stats.CurrentDownloadSpeedMbps.Should().Be(10.5);
        stats.CurrentUploadSpeedMbps.Should().Be(5.2);
    }

    [Fact]
    public void HotspotEvent_ShouldGenerateUniqueId()
    {
        // Arrange & Act
        var event1 = new HotspotEvent { Message = "Test Event 1" };
        var event2 = new HotspotEvent { Message = "Test Event 2" };

        // Assert
        event1.Id.Should().NotBe(Guid.Empty);
        event2.Id.Should().NotBe(Guid.Empty);
        event1.Id.Should().NotBe(event2.Id);
        event1.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
        event2.Timestamp.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromSeconds(5));
    }

    [Theory]
    [InlineData(SecurityType.Open)]
    [InlineData(SecurityType.WEP)]
    [InlineData(SecurityType.WPA_PSK)]
    [InlineData(SecurityType.WPA2_PSK)]
    [InlineData(SecurityType.WPA3_SAE)]
    public void SecurityType_ShouldSupportAllValues(SecurityType securityType)
    {
        // Arrange
        var config = new HotspotConfiguration();

        // Act
        config.Security = securityType;

        // Assert
        config.Security.Should().Be(securityType);
    }

    [Fact]
    public void BandwidthLimits_ShouldAllowZeroForUnlimited()
    {
        // Arrange & Act
        var limits = new BandwidthLimits
        {
            DownloadLimitMbps = 0,
            UploadLimitMbps = 0,
            PerDeviceDownloadLimitMbps = 0,
            PerDeviceUploadLimitMbps = 0
        };

        // Assert
        limits.DownloadLimitMbps.Should().Be(0);
        limits.UploadLimitMbps.Should().Be(0);
        limits.PerDeviceDownloadLimitMbps.Should().Be(0);
        limits.PerDeviceUploadLimitMbps.Should().Be(0);
    }

    [Fact]
    public void DhcpConfiguration_ShouldHaveDefaultValues()
    {
        // Arrange & Act
        var dhcp = new DhcpConfiguration();

        // Assert
        dhcp.StartIP.Should().Be("*************");
        dhcp.EndIP.Should().Be("*************54");
        dhcp.SubnetMask.Should().Be("*************");
        dhcp.Gateway.Should().Be("*************");
        dhcp.LeaseTime.Should().Be(TimeSpan.FromHours(24));
        dhcp.Reservations.Should().NotBeNull();
        dhcp.Reservations.Should().BeEmpty();
    }

    [Fact]
    public void DnsConfiguration_ShouldHaveDefaultValues()
    {
        // Arrange & Act
        var dns = new DnsConfiguration();

        // Assert
        dns.PrimaryDNS.Should().Be("*******");
        dns.SecondaryDNS.Should().Be("*******");
        dns.EnableDnsFiltering.Should().BeFalse();
        dns.LogDnsQueries.Should().BeFalse();
        dns.BlockedDomains.Should().NotBeNull();
        dns.BlockedDomains.Should().BeEmpty();
        dns.AllowedDomains.Should().NotBeNull();
        dns.AllowedDomains.Should().BeEmpty();
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
